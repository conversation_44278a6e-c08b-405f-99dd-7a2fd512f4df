'use client'

import { useTranslations } from 'next-intl'
import { useUser } from '@/components/auth/user-provider'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default function DashboardPage({ params: { locale } }: { params: { locale: string } }) {
  const t = useTranslations()
  const { user } = useUser()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 pb-5">
        <h1 className="text-3xl font-bold leading-6 text-gray-900">
          {t('dashboard.title')}
        </h1>
        <p className="mt-2 max-w-4xl text-sm text-gray-500">
          {t('dashboard.welcome')}
        </p>
      </div>

      {/* Welcome Section */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Welcome, {user?.full_name || user?.email}!
          </h2>
          <p className="text-sm text-gray-600 mb-6">
            This is your SaaS application dashboard. You can manage organizations, facilities, and users from here.
          </p>
          
          {/* Quick Actions */}
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <Link href={`/${locale}/organizations`}>
              <div className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-indigo-50 text-indigo-600 ring-4 ring-white">
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 3h10M9 7h6m-6 4h6m-6 4h6" />
                    </svg>
                  </span>
                </div>
                <div className="mt-8">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    {t('navigation.organizations')}
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Manage your organizations and their settings.
                  </p>
                </div>
              </div>
            </Link>

            <Link href={`/${locale}/facilities`}>
              <div className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-600 ring-4 ring-white">
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 3h10M9 7h6m-6 4h6m-6 4h6" />
                    </svg>
                  </span>
                </div>
                <div className="mt-8">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    {t('navigation.facilities')}
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Manage facilities within your organizations.
                  </p>
                </div>
              </div>
            </Link>

            <Link href={`/${locale}/users`}>
              <div className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-purple-50 text-purple-600 ring-4 ring-white">
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                  </span>
                </div>
                <div className="mt-8">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    {t('navigation.users')}
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Manage users and their roles.
                  </p>
                </div>
              </div>
            </Link>
          </div>
        </div>
      </div>

      {/* Stats or Recent Activity could go here */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {t('dashboard.overview')}
          </h3>
          <p className="text-sm text-gray-600">
            Your application is ready! Start by creating an organization and adding facilities.
          </p>
        </div>
      </div>
    </div>
  )
}
