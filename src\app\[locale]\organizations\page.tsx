'use client'

import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { useUser } from '@/components/auth/user-provider'
import { Button } from '@/components/ui/button'
import { supabase, type Organization } from '@/lib/supabase'
import { authService } from '@/lib/auth'

export default function OrganizationsPage({ params }: { params: { locale: string } }) {
  const locale = params.locale
  const t = useTranslations()
  const { user } = useUser()
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newOrgName, setNewOrgName] = useState('')
  const [newOrgDescription, setNewOrgDescription] = useState('')
  const [creating, setCreating] = useState(false)

  useEffect(() => {
    if (user) {
      fetchOrganizations()
    }
  }, [user])

  const fetchOrganizations = async () => {
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      setOrganizations(data || [])
    } catch (error) {
      console.error('Error fetching organizations:', error)
    } finally {
      setLoading(false)
    }
  }

  const createOrganization = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !newOrgName.trim()) return

    setCreating(true)
    try {
      // Create organization
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .insert({
          name: newOrgName.trim(),
          description: newOrgDescription.trim() || null,
          created_by: user.id,
        })
        .select()
        .single()

      if (orgError) throw orgError

      // Assign admin role to creator
      const { data: adminRole } = await supabase
        .from('roles')
        .select('id')
        .eq('name', 'Admin')
        .single()

      if (adminRole) {
        const { error: roleError } = await supabase
          .from('user_roles')
          .insert({
            user_id: user.id,
            organization_id: orgData.id,
            role_id: adminRole.id,
          })

        if (roleError) throw roleError
      }

      // Refresh organizations list
      await fetchOrganizations()

      // Reset form
      setNewOrgName('')
      setNewOrgDescription('')
      setShowCreateForm(false)
    } catch (error) {
      console.error('Error creating organization:', error)
      alert('Error creating organization. Please try again.')
    } finally {
      setCreating(false)
    }
  }

  if (loading) {
    return <div className="text-center py-8">{t('common.loading')}</div>
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {t('organizations.title')}
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            Manage your organizations and their settings.
          </p>
        </div>
        <Button onClick={() => setShowCreateForm(true)}>
          {t('organizations.createOrganization')}
        </Button>
      </div>

      {/* Create Organization Form */}
      {showCreateForm && (
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            {t('organizations.createOrganization')}
          </h2>
          <form onSubmit={createOrganization} className="space-y-4">
            <div>
              <label htmlFor="orgName" className="block text-sm font-medium text-gray-700">
                {t('organizations.organizationName')}
              </label>
              <input
                type="text"
                id="orgName"
                required
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                value={newOrgName}
                onChange={(e) => setNewOrgName(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="orgDescription" className="block text-sm font-medium text-gray-700">
                {t('organizations.description')}
              </label>
              <textarea
                id="orgDescription"
                rows={3}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                value={newOrgDescription}
                onChange={(e) => setNewOrgDescription(e.target.value)}
              />
            </div>
            <div className="flex space-x-3">
              <Button type="submit" disabled={creating}>
                {creating ? t('common.loading') : t('common.create')}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowCreateForm(false)
                  setNewOrgName('')
                  setNewOrgDescription('')
                }}
              >
                {t('common.cancel')}
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* Organizations List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          {organizations.length === 0 ? (
            <div className="text-center py-8">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 3h10M9 7h6m-6 4h6m-6 4h6"
                />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                {t('organizations.noOrganizations')}
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating your first organization.
              </p>
              <div className="mt-6">
                <Button onClick={() => setShowCreateForm(true)}>
                  {t('organizations.createOrganization')}
                </Button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {organizations.map((org) => (
                <div
                  key={org.id}
                  className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <h3 className="text-lg font-medium text-gray-900">{org.name}</h3>
                  {org.description && (
                    <p className="mt-1 text-sm text-gray-600">{org.description}</p>
                  )}
                  <div className="mt-4 flex space-x-2">
                    <Button size="sm" variant="outline">
                      {t('common.edit')}
                    </Button>
                    <Button size="sm" variant="outline">
                      View Details
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
