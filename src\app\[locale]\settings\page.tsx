'use client'

import { useTranslations } from 'next-intl'
import { useUser } from '@/components/auth/user-provider'

export default function SettingsPage({ params }: { params: { locale: string } }) {
  const locale = params.locale
  const t = useTranslations()
  const { user } = useUser()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {t('navigation.settings')}
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            Manage your account and application settings.
          </p>
        </div>
      </div>

      {/* Profile Settings */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            {t('navigation.profile')}
          </h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                {t('auth.email')}
              </label>
              <p className="mt-1 text-sm text-gray-900">{user?.email}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                {t('auth.fullName')}
              </label>
              <p className="mt-1 text-sm text-gray-900">{user?.full_name || 'Not set'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Application Settings */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Application Settings
          </h2>
          <p className="text-sm text-gray-500">
            Additional settings and preferences will be available here.
          </p>
        </div>
      </div>
    </div>
  )
}
