# Internal VE - SaaS Platform

A comprehensive multi-tenant SaaS application built with Next.js, Supabase, and Tailwind CSS. This application provides a solid foundation for building scalable SaaS products with organizations, facilities, and user management.

## 🚀 Features

### Core Functionality
- **Multi-tenant Architecture**: Organizations → Facilities → Users hierarchy
- **Role-Based Access Control (RBAC)**: Admin, Manager, and Staff roles with granular permissions
- **Authentication**: Complete auth flow with email/password, signup, signin, and password reset
- **Internationalization**: Support for English and Lithuanian languages
- **Responsive Design**: Mobile-first design with Tailwind CSS

### Technical Features
- **Next.js 15**: Latest version with App Router
- **Supabase Integration**: Authentication, database, and Row-Level Security (RLS)
- **TypeScript**: Full type safety throughout the application
- **Modern UI**: Clean, professional interface with Tailwind CSS
- **Database Security**: Comprehensive RLS policies for data isolation

## 🛠 Tech Stack

| Component | Technology |
|-----------|------------|
| Frontend | Next.js 15 (App Router) |
| Styling | Tailwind CSS |
| Backend | Supabase |
| Database | PostgreSQL (via Supabase) |
| Authentication | Supabase Auth |
| Language | TypeScript |
| Internationalization | next-intl |
| State Management | React Context |

## 📊 Database Schema

### Core Tables
- **users**: User profiles linked to Supabase auth
- **organizations**: Top-level tenant entities
- **facilities**: Sub-entities within organizations
- **roles**: Predefined roles (Admin, Manager, Staff)
- **permissions**: Granular permissions for different actions
- **user_roles**: Many-to-many relationship for user role assignments
- **role_permissions**: Many-to-many relationship for role permissions

### Security
- Row-Level Security (RLS) policies ensure data isolation
- Users can only access data within their assigned organizations
- Role-based permissions control what actions users can perform

## 🚦 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/AndriusAppDev/internal-ve.git
   cd internal-ve
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**

   The application is already configured with a Supabase project. The environment variables are set in `.env.local`:
   ```
   NEXT_PUBLIC_SUPABASE_URL=https://hdompiwbxzymopmdhwbq.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**

   Navigate to [http://localhost:3000](http://localhost:3000)

## 🏗 Project Structure

```
src/
├── app/                          # Next.js App Router
│   ├── [locale]/                 # Internationalized routes
│   │   ├── auth/                 # Authentication pages
│   │   ├── dashboard/            # Main dashboard
│   │   ├── organizations/        # Organization management
│   │   ├── facilities/           # Facility management
│   │   ├── users/                # User management
│   │   └── settings/             # Settings pages
│   ├── globals.css               # Global styles
│   └── layout.tsx                # Root layout
├── components/                   # Reusable components
│   ├── auth/                     # Authentication components
│   ├── layout/                   # Layout components
│   └── ui/                       # UI components
├── lib/                          # Utility libraries
│   ├── auth.ts                   # Authentication utilities
│   ├── supabase.ts               # Supabase client and types
│   └── utils.ts                  # General utilities
├── locales/                      # Translation files
│   ├── en.json                   # English translations
│   └── lt.json                   # Lithuanian translations
└── i18n/                         # Internationalization config
    └── request.ts                # i18n request configuration
```

## 🔐 Authentication & Authorization

### User Roles
- **Admin**: Full access to organization and all facilities
- **Manager**: Manage facilities and users within assigned facilities
- **Staff**: Basic access to assigned facilities

### Permissions
The system includes granular permissions for:
- Organization management (create, read, update, delete)
- Facility management (create, read, update, delete)
- User management (create, read, update, delete)
- Role assignment (assign roles to users)

## 🌍 Internationalization

The application supports multiple languages:
- **English** (default)
- **Lithuanian**

Language switching is available in the dashboard header.

## 🚀 Deployment

The application is ready for deployment on platforms like:
- **Vercel** (recommended for Next.js)
- **Netlify**
- **Railway**
- **AWS Amplify**

### Environment Variables for Production
Make sure to set the following environment variables in your deployment platform:
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`

## 📝 Usage

### Getting Started
1. **Sign Up**: Create a new account at `/en/auth/signup`
2. **Create Organization**: After signing in, create your first organization
3. **Add Facilities**: Add facilities within your organization
4. **Invite Users**: Invite team members and assign roles
5. **Manage**: Use the dashboard to manage your organization

### User Workflow
1. Users sign up and are automatically assigned to organizations
2. Admins can create organizations and facilities
3. Managers can manage facilities and invite users
4. Staff members have read access to their assigned facilities

## 🔧 Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Database Migrations
The database schema is already set up in Supabase with:
- All necessary tables created
- RLS policies configured
- Initial roles and permissions seeded

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the ISC License.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) for the amazing React framework
- [Supabase](https://supabase.com/) for the backend infrastructure
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [next-intl](https://next-intl-docs.vercel.app/) for internationalization

---

**Built with ❤️ for scalable SaaS applications**