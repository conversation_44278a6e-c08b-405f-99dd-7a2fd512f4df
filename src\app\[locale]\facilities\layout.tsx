'use client'

import { UserProvider } from '@/components/auth/user-provider'
import { DashboardLayout } from '@/components/layout/dashboard-layout'

export default function FacilitiesLayoutWrapper({
  children,
  params
}: {
  children: React.ReactNode
  params: { locale: string }
}) {
  const locale = params.locale
  return (
    <UserProvider>
      <DashboardLayout locale={locale}>
        {children}
      </DashboardLayout>
    </UserProvider>
  )
}
