import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface User {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

export interface Organization {
  id: string
  name: string
  description?: string
  created_by?: string
  created_at: string
  updated_at: string
}

export interface Facility {
  id: string
  organization_id: string
  name: string
  description?: string
  address?: string
  created_by?: string
  created_at: string
  updated_at: string
}

export interface Role {
  id: string
  name: string
  description?: string
  created_at: string
}

export interface Permission {
  id: string
  name: string
  description?: string
  resource: string
  action: string
  created_at: string
}

export interface UserRole {
  id: string
  user_id: string
  organization_id: string
  facility_id?: string
  role_id: string
  created_at: string
  role?: Role
  organization?: Organization
  facility?: Facility
}

export interface RolePermission {
  id: string
  role_id: string
  permission_id: string
  created_at: string
  permission?: Permission
}
