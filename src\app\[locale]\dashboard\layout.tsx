'use client'

import { UserProvider } from '@/components/auth/user-provider'
import { DashboardLayout } from '@/components/layout/dashboard-layout'

export default function DashboardLayoutWrapper({
  children,
  params: { locale }
}: {
  children: React.ReactNode
  params: { locale: string }
}) {
  return (
    <UserProvider>
      <DashboardLayout locale={locale}>
        {children}
      </DashboardLayout>
    </UserProvider>
  )
}
