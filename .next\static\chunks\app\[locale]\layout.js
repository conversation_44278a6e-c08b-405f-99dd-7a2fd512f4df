/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/[locale]/layout"],{

/***/ "(app-pages-browser)/./node_modules/@formatjs/fast-memoize/lib/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/@formatjs/fast-memoize/lib/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoize: () => (/* binding */ memoize),\n/* harmony export */   strategies: () => (/* binding */ strategies)\n/* harmony export */ });\n//\n// Main\n//\nfunction memoize(fn, options) {\n    var cache = options && options.cache ? options.cache : cacheDefault;\n    var serializer = options && options.serializer ? options.serializer : serializerDefault;\n    var strategy = options && options.strategy ? options.strategy : strategyDefault;\n    return strategy(fn, {\n        cache: cache,\n        serializer: serializer,\n    });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n    return (value == null || typeof value === 'number' || typeof value === 'boolean'); // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n    var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.call(this, arg);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n    var args = Array.prototype.slice.call(arguments, 3);\n    var cacheKey = serializer(args);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.apply(this, args);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n    return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n    var strategy = fn.length === 1 ? monadic : variadic;\n    return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n    return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n    return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n    return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nvar ObjectWithoutPrototypeCache = /** @class */ (function () {\n    function ObjectWithoutPrototypeCache() {\n        this.cache = Object.create(null);\n    }\n    ObjectWithoutPrototypeCache.prototype.get = function (key) {\n        return this.cache[key];\n    };\n    ObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n        this.cache[key] = value;\n    };\n    return ObjectWithoutPrototypeCache;\n}());\nvar cacheDefault = {\n    create: function create() {\n        return new ObjectWithoutPrototypeCache();\n    },\n};\nvar strategies = {\n    variadic: strategyVariadic,\n    monadic: strategyMonadic,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/fast-memoize/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBestPattern: () => (/* binding */ getBestPattern)\n/* harmony export */ });\n/* harmony import */ var _time_data_generated__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./time-data.generated */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js\");\n\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nfunction getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[regionTag || ''] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[languageTag || ''] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[\"\".concat(languageTag, \"-001\")] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData['001'];\n    return hourCycles[0];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/error.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@formatjs/icu-messageformat-parser/lib/error.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorKind: () => (/* binding */ ErrorKind)\n/* harmony export */ });\nvar ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (ErrorKind = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@formatjs/icu-messageformat-parser/lib/index.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SKELETON_TYPE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.SKELETON_TYPE),\n/* harmony export */   TYPE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.TYPE),\n/* harmony export */   _Parser: () => (/* binding */ _Parser),\n/* harmony export */   createLiteralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.createLiteralElement),\n/* harmony export */   createNumberElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.createNumberElement),\n/* harmony export */   isArgumentElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isArgumentElement),\n/* harmony export */   isDateElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isDateElement),\n/* harmony export */   isDateTimeSkeleton: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isDateTimeSkeleton),\n/* harmony export */   isLiteralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isLiteralElement),\n/* harmony export */   isNumberElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isNumberElement),\n/* harmony export */   isNumberSkeleton: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isNumberSkeleton),\n/* harmony export */   isPluralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isPluralElement),\n/* harmony export */   isPoundElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isPoundElement),\n/* harmony export */   isSelectElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isSelectElement),\n/* harmony export */   isStructurallySame: () => (/* reexport safe */ _manipulator__WEBPACK_IMPORTED_MODULE_4__.isStructurallySame),\n/* harmony export */   isTagElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isTagElement),\n/* harmony export */   isTimeElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isTimeElement),\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/error.js\");\n/* harmony import */ var _parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parser */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/parser.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n/* harmony import */ var _manipulator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./manipulator */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js\");\n\n\n\n\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isSelectElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_2__.isPluralElement)(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isNumberElement)(el) && (0,_types__WEBPACK_IMPORTED_MODULE_2__.isNumberSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if (((0,_types__WEBPACK_IMPORTED_MODULE_2__.isDateElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_2__.isTimeElement)(el)) &&\n            (0,_types__WEBPACK_IMPORTED_MODULE_2__.isDateTimeSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isTagElement)(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nfunction parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = (0,tslib__WEBPACK_IMPORTED_MODULE_3__.__assign)({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new _parser__WEBPACK_IMPORTED_MODULE_1__.Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\n\n// only for testing\nvar _Parser = _parser__WEBPACK_IMPORTED_MODULE_1__.Parser;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LW1lc3NhZ2Vmb3JtYXQtcGFyc2VyL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFpQztBQUNHO0FBQ0Y7QUFDNkg7QUFDL0o7QUFDQTtBQUNBO0FBQ0EsWUFBWSx1REFBZSxRQUFRLHVEQUFlO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsdURBQWUsUUFBUSx3REFBZ0I7QUFDeEQ7QUFDQTtBQUNBLGtCQUFrQixxREFBYSxRQUFRLHFEQUFhO0FBQ3BELFlBQVksMERBQWtCO0FBQzlCO0FBQ0E7QUFDQSxpQkFBaUIsb0RBQVk7QUFDN0I7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNPO0FBQ1AsMkJBQTJCO0FBQzNCLFdBQVcsK0NBQVEsR0FBRyx1REFBdUQ7QUFDN0UscUJBQXFCLDJDQUFNO0FBQzNCO0FBQ0EsZ0NBQWdDLDZDQUFTO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDd0I7QUFDeEI7QUFDTyxjQUFjLDJDQUFNO0FBQ3dCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFwcCBEZXZcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcaW50ZXJuYWwtdmVcXG5vZGVfbW9kdWxlc1xcQGZvcm1hdGpzXFxpY3UtbWVzc2FnZWZvcm1hdC1wYXJzZXJcXGxpYlxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgX19hc3NpZ24gfSBmcm9tIFwidHNsaWJcIjtcbmltcG9ydCB7IEVycm9yS2luZCB9IGZyb20gJy4vZXJyb3InO1xuaW1wb3J0IHsgUGFyc2VyIH0gZnJvbSAnLi9wYXJzZXInO1xuaW1wb3J0IHsgaXNEYXRlRWxlbWVudCwgaXNEYXRlVGltZVNrZWxldG9uLCBpc051bWJlckVsZW1lbnQsIGlzTnVtYmVyU2tlbGV0b24sIGlzUGx1cmFsRWxlbWVudCwgaXNTZWxlY3RFbGVtZW50LCBpc1RhZ0VsZW1lbnQsIGlzVGltZUVsZW1lbnQsIH0gZnJvbSAnLi90eXBlcyc7XG5mdW5jdGlvbiBwcnVuZUxvY2F0aW9uKGVscykge1xuICAgIGVscy5mb3JFYWNoKGZ1bmN0aW9uIChlbCkge1xuICAgICAgICBkZWxldGUgZWwubG9jYXRpb247XG4gICAgICAgIGlmIChpc1NlbGVjdEVsZW1lbnQoZWwpIHx8IGlzUGx1cmFsRWxlbWVudChlbCkpIHtcbiAgICAgICAgICAgIGZvciAodmFyIGsgaW4gZWwub3B0aW9ucykge1xuICAgICAgICAgICAgICAgIGRlbGV0ZSBlbC5vcHRpb25zW2tdLmxvY2F0aW9uO1xuICAgICAgICAgICAgICAgIHBydW5lTG9jYXRpb24oZWwub3B0aW9uc1trXS52YWx1ZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoaXNOdW1iZXJFbGVtZW50KGVsKSAmJiBpc051bWJlclNrZWxldG9uKGVsLnN0eWxlKSkge1xuICAgICAgICAgICAgZGVsZXRlIGVsLnN0eWxlLmxvY2F0aW9uO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKChpc0RhdGVFbGVtZW50KGVsKSB8fCBpc1RpbWVFbGVtZW50KGVsKSkgJiZcbiAgICAgICAgICAgIGlzRGF0ZVRpbWVTa2VsZXRvbihlbC5zdHlsZSkpIHtcbiAgICAgICAgICAgIGRlbGV0ZSBlbC5zdHlsZS5sb2NhdGlvbjtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChpc1RhZ0VsZW1lbnQoZWwpKSB7XG4gICAgICAgICAgICBwcnVuZUxvY2F0aW9uKGVsLmNoaWxkcmVuKTtcbiAgICAgICAgfVxuICAgIH0pO1xufVxuZXhwb3J0IGZ1bmN0aW9uIHBhcnNlKG1lc3NhZ2UsIG9wdHMpIHtcbiAgICBpZiAob3B0cyA9PT0gdm9pZCAwKSB7IG9wdHMgPSB7fTsgfVxuICAgIG9wdHMgPSBfX2Fzc2lnbih7IHNob3VsZFBhcnNlU2tlbGV0b25zOiB0cnVlLCByZXF1aXJlc090aGVyQ2xhdXNlOiB0cnVlIH0sIG9wdHMpO1xuICAgIHZhciByZXN1bHQgPSBuZXcgUGFyc2VyKG1lc3NhZ2UsIG9wdHMpLnBhcnNlKCk7XG4gICAgaWYgKHJlc3VsdC5lcnIpIHtcbiAgICAgICAgdmFyIGVycm9yID0gU3ludGF4RXJyb3IoRXJyb3JLaW5kW3Jlc3VsdC5lcnIua2luZF0pO1xuICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yIEFzc2lnbiB0byBlcnJvciBvYmplY3RcbiAgICAgICAgZXJyb3IubG9jYXRpb24gPSByZXN1bHQuZXJyLmxvY2F0aW9uO1xuICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yIEFzc2lnbiB0byBlcnJvciBvYmplY3RcbiAgICAgICAgZXJyb3Iub3JpZ2luYWxNZXNzYWdlID0gcmVzdWx0LmVyci5tZXNzYWdlO1xuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gICAgaWYgKCEob3B0cyA9PT0gbnVsbCB8fCBvcHRzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvcHRzLmNhcHR1cmVMb2NhdGlvbikpIHtcbiAgICAgICAgcHJ1bmVMb2NhdGlvbihyZXN1bHQudmFsKTtcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdC52YWw7XG59XG5leHBvcnQgKiBmcm9tICcuL3R5cGVzJztcbi8vIG9ubHkgZm9yIHRlc3RpbmdcbmV4cG9ydCB2YXIgX1BhcnNlciA9IFBhcnNlcjtcbmV4cG9ydCB7IGlzU3RydWN0dXJhbGx5U2FtZSB9IGZyb20gJy4vbWFuaXB1bGF0b3InO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hoistSelectors: () => (/* binding */ hoistSelectors),\n/* harmony export */   isStructurallySame: () => (/* binding */ isStructurallySame)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n\n\nfunction cloneDeep(obj) {\n    if (Array.isArray(obj)) {\n        // @ts-expect-error meh\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)([], obj.map(cloneDeep), true);\n    }\n    if (obj !== null && typeof obj === 'object') {\n        // @ts-expect-error meh\n        return Object.keys(obj).reduce(function (cloned, k) {\n            // @ts-expect-error meh\n            cloned[k] = cloneDeep(obj[k]);\n            return cloned;\n        }, {});\n    }\n    return obj;\n}\nfunction hoistPluralOrSelectElement(ast, el, positionToInject) {\n    // pull this out of the ast and move it to the top\n    var cloned = cloneDeep(el);\n    var options = cloned.options;\n    cloned.options = Object.keys(options).reduce(function (all, k) {\n        var newValue = hoistSelectors((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)([], ast.slice(0, positionToInject), true), options[k].value, true), ast.slice(positionToInject + 1), true));\n        all[k] = {\n            value: newValue,\n        };\n        return all;\n    }, {});\n    return cloned;\n}\nfunction isPluralOrSelectElement(el) {\n    return (0,_types__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el);\n}\nfunction findPluralOrSelectElement(ast) {\n    return !!ast.find(function (el) {\n        if (isPluralOrSelectElement(el)) {\n            return true;\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            return findPluralOrSelectElement(el.children);\n        }\n        return false;\n    });\n}\n/**\n * Hoist all selectors to the beginning of the AST & flatten the\n * resulting options. E.g:\n * \"I have {count, plural, one{a dog} other{many dogs}}\"\n * becomes \"{count, plural, one{I have a dog} other{I have many dogs}}\".\n * If there are multiple selectors, the order of which one is hoisted 1st\n * is non-deterministic.\n * The goal is to provide as many full sentences as possible since fragmented\n * sentences are not translator-friendly\n * @param ast AST\n */\nfunction hoistSelectors(ast) {\n    for (var i = 0; i < ast.length; i++) {\n        var el = ast[i];\n        if (isPluralOrSelectElement(el)) {\n            return [hoistPluralOrSelectElement(ast, el, i)];\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el) && findPluralOrSelectElement([el])) {\n            throw new Error('Cannot hoist plural/select within a tag element. Please put the tag element inside each plural/select option');\n        }\n    }\n    return ast;\n}\n/**\n * Collect all variables in an AST to Record<string, TYPE>\n * @param ast AST to collect variables from\n * @param vars Record of variable name to variable type\n */\nfunction collectVariables(ast, vars) {\n    if (vars === void 0) { vars = new Map(); }\n    ast.forEach(function (el) {\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isArgumentElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isDateElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isTimeElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isNumberElement)(el)) {\n            if (el.value in vars && vars.get(el.value) !== el.type) {\n                throw new Error(\"Variable \".concat(el.value, \" has conflicting types\"));\n            }\n            vars.set(el.value, el.type);\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el)) {\n            vars.set(el.value, el.type);\n            Object.keys(el.options).forEach(function (k) {\n                collectVariables(el.options[k].value, vars);\n            });\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            vars.set(el.value, el.type);\n            collectVariables(el.children, vars);\n        }\n    });\n}\n/**\n * Check if 2 ASTs are structurally the same. This primarily means that\n * they have the same variables with the same type\n * @param a\n * @param b\n * @returns\n */\nfunction isStructurallySame(a, b) {\n    var aVars = new Map();\n    var bVars = new Map();\n    collectVariables(a, aVars);\n    collectVariables(b, bVars);\n    if (aVars.size !== bVars.size) {\n        return {\n            success: false,\n            error: new Error(\"Different number of variables: [\".concat(Array.from(aVars.keys()).join(', '), \"] vs [\").concat(Array.from(bVars.keys()).join(', '), \"]\")),\n        };\n    }\n    return Array.from(aVars.entries()).reduce(function (result, _a) {\n        var key = _a[0], type = _a[1];\n        if (!result.success) {\n            return result;\n        }\n        var bType = bVars.get(key);\n        if (bType == null) {\n            return {\n                success: false,\n                error: new Error(\"Missing variable \".concat(key, \" in message\")),\n            };\n        }\n        if (bType !== type) {\n            return {\n                success: false,\n                error: new Error(\"Variable \".concat(key, \" has conflicting types: \").concat(_types__WEBPACK_IMPORTED_MODULE_0__.TYPE[type], \" vs \").concat(_types__WEBPACK_IMPORTED_MODULE_0__.TYPE[bType])),\n            };\n        }\n        return result;\n    }, { success: true });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/parser.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@formatjs/icu-messageformat-parser/lib/parser.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/error.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n/* harmony import */ var _regex_generated__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./regex.generated */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js\");\n/* harmony import */ var _formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @formatjs/icu-skeleton-parser */ \"(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/index.js\");\n/* harmony import */ var _date_time_pattern_generator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./date-time-pattern-generator */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js\");\nvar _a;\n\n\n\n\n\n\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(_regex_generated__WEBPACK_IMPORTED_MODULE_2__.SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(_regex_generated__WEBPACK_IMPORTED_MODULE_2__.SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith && '_a'.startsWith('a', 1);\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = (0,_date_time_pattern_generator__WEBPACK_IMPORTED_MODULE_4__.getBestPattern)(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseDateTimeSkeleton)(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.date : _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.number\n                            : argType === 'date'\n                                ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.date\n                                : _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, (0,tslib__WEBPACK_IMPORTED_MODULE_5__.__assign)({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseNumberSkeletonFromString)(skeleton);\n        }\n        catch (e) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: _types__WEBPACK_IMPORTED_MODULE_1__.SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseNumberSkeleton)(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\n\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/parser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SPACE_SEPARATOR_REGEX: () => (/* binding */ SPACE_SEPARATOR_REGEX),\n/* harmony export */   WHITE_SPACE_REGEX: () => (/* binding */ WHITE_SPACE_REGEX)\n/* harmony export */ });\n// @generated from regex-gen.ts\nvar SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nvar WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LW1lc3NhZ2Vmb3JtYXQtcGFyc2VyL2xpYi9yZWdleC5nZW5lcmF0ZWQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNPO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXBwIERldlxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxpbnRlcm5hbC12ZVxcbm9kZV9tb2R1bGVzXFxAZm9ybWF0anNcXGljdS1tZXNzYWdlZm9ybWF0LXBhcnNlclxcbGliXFxyZWdleC5nZW5lcmF0ZWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQGdlbmVyYXRlZCBmcm9tIHJlZ2V4LWdlbi50c1xuZXhwb3J0IHZhciBTUEFDRV9TRVBBUkFUT1JfUkVHRVggPSAvWyBcXHhBMFxcdTE2ODBcXHUyMDAwLVxcdTIwMEFcXHUyMDJGXFx1MjA1RlxcdTMwMDBdLztcbmV4cG9ydCB2YXIgV0hJVEVfU1BBQ0VfUkVHRVggPSAvW1xcdC1cXHIgXFx4ODVcXHUyMDBFXFx1MjAwRlxcdTIwMjhcXHUyMDI5XS87XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeData: () => (/* binding */ timeData)\n/* harmony export */ });\n// @generated from time-data-gen.ts\n// prettier-ignore  \nvar timeData = {\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"419\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AX\": [\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BI\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"BO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BY\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CL\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GT\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HN\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"K\",\n        \"h\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MG\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MT\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MV\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MX\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NI\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"RW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SV\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TH\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TJ\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TM\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VN\": [\n        \"H\",\n        \"h\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-HK\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-IL\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"en-MY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/types.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@formatjs/icu-messageformat-parser/lib/types.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SKELETON_TYPE: () => (/* binding */ SKELETON_TYPE),\n/* harmony export */   TYPE: () => (/* binding */ TYPE),\n/* harmony export */   createLiteralElement: () => (/* binding */ createLiteralElement),\n/* harmony export */   createNumberElement: () => (/* binding */ createNumberElement),\n/* harmony export */   isArgumentElement: () => (/* binding */ isArgumentElement),\n/* harmony export */   isDateElement: () => (/* binding */ isDateElement),\n/* harmony export */   isDateTimeSkeleton: () => (/* binding */ isDateTimeSkeleton),\n/* harmony export */   isLiteralElement: () => (/* binding */ isLiteralElement),\n/* harmony export */   isNumberElement: () => (/* binding */ isNumberElement),\n/* harmony export */   isNumberSkeleton: () => (/* binding */ isNumberSkeleton),\n/* harmony export */   isPluralElement: () => (/* binding */ isPluralElement),\n/* harmony export */   isPoundElement: () => (/* binding */ isPoundElement),\n/* harmony export */   isSelectElement: () => (/* binding */ isSelectElement),\n/* harmony export */   isTagElement: () => (/* binding */ isTagElement),\n/* harmony export */   isTimeElement: () => (/* binding */ isTimeElement)\n/* harmony export */ });\nvar TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (TYPE = {}));\nvar SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nfunction isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nfunction isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nfunction isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nfunction isDateElement(el) {\n    return el.type === TYPE.date;\n}\nfunction isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nfunction isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nfunction isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nfunction isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nfunction isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nfunction isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nfunction isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nfunction createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nfunction createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/types.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDateTimeSkeleton: () => (/* binding */ parseDateTimeSkeleton)\n/* harmony export */ });\n/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nfunction parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: milliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@formatjs/icu-skeleton-parser/lib/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDateTimeSkeleton: () => (/* reexport safe */ _date_time__WEBPACK_IMPORTED_MODULE_0__.parseDateTimeSkeleton),\n/* harmony export */   parseNumberSkeleton: () => (/* reexport safe */ _number__WEBPACK_IMPORTED_MODULE_1__.parseNumberSkeleton),\n/* harmony export */   parseNumberSkeletonFromString: () => (/* reexport safe */ _number__WEBPACK_IMPORTED_MODULE_1__.parseNumberSkeletonFromString)\n/* harmony export */ });\n/* harmony import */ var _date_time__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./date-time */ \"(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js\");\n/* harmony import */ var _number__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./number */ \"(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/number.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LXNrZWxldG9uLXBhcnNlci9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEI7QUFDSCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBcHAgRGV2XFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGludGVybmFsLXZlXFxub2RlX21vZHVsZXNcXEBmb3JtYXRqc1xcaWN1LXNrZWxldG9uLXBhcnNlclxcbGliXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL2RhdGUtdGltZSc7XG5leHBvcnQgKiBmcm9tICcuL251bWJlcic7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/number.js":
/*!******************************************************************!*\
  !*** ./node_modules/@formatjs/icu-skeleton-parser/lib/number.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseNumberSkeleton: () => (/* binding */ parseNumberSkeleton),\n/* harmony export */   parseNumberSkeletonFromString: () => (/* binding */ parseNumberSkeletonFromString)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _regex_generated__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.generated */ \"(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js\");\n\n\nfunction parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(_regex_generated__WEBPACK_IMPORTED_MODULE_0__.WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nfunction parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            case 'rounding-mode-floor':\n                result.roundingMode = 'floor';\n                continue;\n            case 'rounding-mode-ceiling':\n                result.roundingMode = 'ceil';\n                continue;\n            case 'rounding-mode-down':\n                result.roundingMode = 'trunc';\n                continue;\n            case 'rounding-mode-up':\n                result.roundingMode = 'expand';\n                continue;\n            case 'rounding-mode-half-even':\n                result.roundingMode = 'halfEven';\n                continue;\n            case 'rounding-mode-half-down':\n                result.roundingMode = 'halfTrunc';\n                continue;\n            case 'rounding-mode-half-up':\n                result.roundingMode = 'halfExpand';\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LXNrZWxldG9uLXBhcnNlci9saWIvbnVtYmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBaUM7QUFDcUI7QUFDL0M7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSwrREFBaUI7QUFDaEMsK0JBQStCLHNCQUFzQjtBQUNyRDtBQUNBLG9EQUFvRCw0QkFBNEI7QUFDaEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQThDLHVCQUF1QjtBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDhCQUE4QjtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0Esd0NBQXdDLHNCQUFzQjtBQUM5RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLCtDQUFRLENBQUMsK0NBQVEsQ0FBQywrQ0FBUSxHQUFHLGFBQWEsd0JBQXdCLDhDQUE4QyxRQUFRLCtDQUFRLENBQUMsK0NBQVEsR0FBRyxzQ0FBc0MsSUFBSTtBQUMvTTtBQUNBO0FBQ0EseUJBQXlCLCtDQUFRLENBQUMsK0NBQVEsQ0FBQywrQ0FBUSxHQUFHLGFBQWEseUJBQXlCLDhDQUE4QyxRQUFRLCtDQUFRLENBQUMsK0NBQVEsR0FBRyxzQ0FBc0MsSUFBSTtBQUNoTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QiwrQ0FBUSxDQUFDLCtDQUFRLEdBQUcsYUFBYSx1Q0FBdUM7QUFDakc7QUFDQTtBQUNBLHlCQUF5QiwrQ0FBUSxDQUFDLCtDQUFRLEdBQUc7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQiwrQ0FBUSxDQUFDLCtDQUFRLEdBQUc7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsK0NBQVEsQ0FBQywrQ0FBUSxHQUFHO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQiwrQ0FBUSxDQUFDLCtDQUFRLEdBQUc7QUFDekM7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXBwIERldlxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxpbnRlcm5hbC12ZVxcbm9kZV9tb2R1bGVzXFxAZm9ybWF0anNcXGljdS1za2VsZXRvbi1wYXJzZXJcXGxpYlxcbnVtYmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IF9fYXNzaWduIH0gZnJvbSBcInRzbGliXCI7XG5pbXBvcnQgeyBXSElURV9TUEFDRV9SRUdFWCB9IGZyb20gJy4vcmVnZXguZ2VuZXJhdGVkJztcbmV4cG9ydCBmdW5jdGlvbiBwYXJzZU51bWJlclNrZWxldG9uRnJvbVN0cmluZyhza2VsZXRvbikge1xuICAgIGlmIChza2VsZXRvbi5sZW5ndGggPT09IDApIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdOdW1iZXIgc2tlbGV0b24gY2Fubm90IGJlIGVtcHR5Jyk7XG4gICAgfVxuICAgIC8vIFBhcnNlIHRoZSBza2VsZXRvblxuICAgIHZhciBzdHJpbmdUb2tlbnMgPSBza2VsZXRvblxuICAgICAgICAuc3BsaXQoV0hJVEVfU1BBQ0VfUkVHRVgpXG4gICAgICAgIC5maWx0ZXIoZnVuY3Rpb24gKHgpIHsgcmV0dXJuIHgubGVuZ3RoID4gMDsgfSk7XG4gICAgdmFyIHRva2VucyA9IFtdO1xuICAgIGZvciAodmFyIF9pID0gMCwgc3RyaW5nVG9rZW5zXzEgPSBzdHJpbmdUb2tlbnM7IF9pIDwgc3RyaW5nVG9rZW5zXzEubGVuZ3RoOyBfaSsrKSB7XG4gICAgICAgIHZhciBzdHJpbmdUb2tlbiA9IHN0cmluZ1Rva2Vuc18xW19pXTtcbiAgICAgICAgdmFyIHN0ZW1BbmRPcHRpb25zID0gc3RyaW5nVG9rZW4uc3BsaXQoJy8nKTtcbiAgICAgICAgaWYgKHN0ZW1BbmRPcHRpb25zLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIG51bWJlciBza2VsZXRvbicpO1xuICAgICAgICB9XG4gICAgICAgIHZhciBzdGVtID0gc3RlbUFuZE9wdGlvbnNbMF0sIG9wdGlvbnMgPSBzdGVtQW5kT3B0aW9ucy5zbGljZSgxKTtcbiAgICAgICAgZm9yICh2YXIgX2EgPSAwLCBvcHRpb25zXzEgPSBvcHRpb25zOyBfYSA8IG9wdGlvbnNfMS5sZW5ndGg7IF9hKyspIHtcbiAgICAgICAgICAgIHZhciBvcHRpb24gPSBvcHRpb25zXzFbX2FdO1xuICAgICAgICAgICAgaWYgKG9wdGlvbi5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgbnVtYmVyIHNrZWxldG9uJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgdG9rZW5zLnB1c2goeyBzdGVtOiBzdGVtLCBvcHRpb25zOiBvcHRpb25zIH0pO1xuICAgIH1cbiAgICByZXR1cm4gdG9rZW5zO1xufVxuZnVuY3Rpb24gaWN1VW5pdFRvRWNtYSh1bml0KSB7XG4gICAgcmV0dXJuIHVuaXQucmVwbGFjZSgvXiguKj8pLS8sICcnKTtcbn1cbnZhciBGUkFDVElPTl9QUkVDSVNJT05fUkVHRVggPSAvXlxcLig/OigwKykoXFwqKT98KCMrKXwoMCspKCMrKSkkL2c7XG52YXIgU0lHTklGSUNBTlRfUFJFQ0lTSU9OX1JFR0VYID0gL14oQCspPyhcXCt8IyspP1tyc10/JC9nO1xudmFyIElOVEVHRVJfV0lEVEhfUkVHRVggPSAvKFxcKikoMCspfCgjKykoMCspfCgwKykvZztcbnZhciBDT05DSVNFX0lOVEVHRVJfV0lEVEhfUkVHRVggPSAvXigwKykkLztcbmZ1bmN0aW9uIHBhcnNlU2lnbmlmaWNhbnRQcmVjaXNpb24oc3RyKSB7XG4gICAgdmFyIHJlc3VsdCA9IHt9O1xuICAgIGlmIChzdHJbc3RyLmxlbmd0aCAtIDFdID09PSAncicpIHtcbiAgICAgICAgcmVzdWx0LnJvdW5kaW5nUHJpb3JpdHkgPSAnbW9yZVByZWNpc2lvbic7XG4gICAgfVxuICAgIGVsc2UgaWYgKHN0cltzdHIubGVuZ3RoIC0gMV0gPT09ICdzJykge1xuICAgICAgICByZXN1bHQucm91bmRpbmdQcmlvcml0eSA9ICdsZXNzUHJlY2lzaW9uJztcbiAgICB9XG4gICAgc3RyLnJlcGxhY2UoU0lHTklGSUNBTlRfUFJFQ0lTSU9OX1JFR0VYLCBmdW5jdGlvbiAoXywgZzEsIGcyKSB7XG4gICAgICAgIC8vIEBAQCBjYXNlXG4gICAgICAgIGlmICh0eXBlb2YgZzIgIT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICByZXN1bHQubWluaW11bVNpZ25pZmljYW50RGlnaXRzID0gZzEubGVuZ3RoO1xuICAgICAgICAgICAgcmVzdWx0Lm1heGltdW1TaWduaWZpY2FudERpZ2l0cyA9IGcxLmxlbmd0aDtcbiAgICAgICAgfVxuICAgICAgICAvLyBAQEArIGNhc2VcbiAgICAgICAgZWxzZSBpZiAoZzIgPT09ICcrJykge1xuICAgICAgICAgICAgcmVzdWx0Lm1pbmltdW1TaWduaWZpY2FudERpZ2l0cyA9IGcxLmxlbmd0aDtcbiAgICAgICAgfVxuICAgICAgICAvLyAuIyMjIGNhc2VcbiAgICAgICAgZWxzZSBpZiAoZzFbMF0gPT09ICcjJykge1xuICAgICAgICAgICAgcmVzdWx0Lm1heGltdW1TaWduaWZpY2FudERpZ2l0cyA9IGcxLmxlbmd0aDtcbiAgICAgICAgfVxuICAgICAgICAvLyAuQEAjIyBvciAuQEBAIGNhc2VcbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICByZXN1bHQubWluaW11bVNpZ25pZmljYW50RGlnaXRzID0gZzEubGVuZ3RoO1xuICAgICAgICAgICAgcmVzdWx0Lm1heGltdW1TaWduaWZpY2FudERpZ2l0cyA9XG4gICAgICAgICAgICAgICAgZzEubGVuZ3RoICsgKHR5cGVvZiBnMiA9PT0gJ3N0cmluZycgPyBnMi5sZW5ndGggOiAwKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gJyc7XG4gICAgfSk7XG4gICAgcmV0dXJuIHJlc3VsdDtcbn1cbmZ1bmN0aW9uIHBhcnNlU2lnbihzdHIpIHtcbiAgICBzd2l0Y2ggKHN0cikge1xuICAgICAgICBjYXNlICdzaWduLWF1dG8nOlxuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzaWduRGlzcGxheTogJ2F1dG8nLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgY2FzZSAnc2lnbi1hY2NvdW50aW5nJzpcbiAgICAgICAgY2FzZSAnKCknOlxuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBjdXJyZW5jeVNpZ246ICdhY2NvdW50aW5nJyxcbiAgICAgICAgICAgIH07XG4gICAgICAgIGNhc2UgJ3NpZ24tYWx3YXlzJzpcbiAgICAgICAgY2FzZSAnKyEnOlxuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzaWduRGlzcGxheTogJ2Fsd2F5cycsXG4gICAgICAgICAgICB9O1xuICAgICAgICBjYXNlICdzaWduLWFjY291bnRpbmctYWx3YXlzJzpcbiAgICAgICAgY2FzZSAnKCkhJzpcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc2lnbkRpc3BsYXk6ICdhbHdheXMnLFxuICAgICAgICAgICAgICAgIGN1cnJlbmN5U2lnbjogJ2FjY291bnRpbmcnLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgY2FzZSAnc2lnbi1leGNlcHQtemVybyc6XG4gICAgICAgIGNhc2UgJys/JzpcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc2lnbkRpc3BsYXk6ICdleGNlcHRaZXJvJyxcbiAgICAgICAgICAgIH07XG4gICAgICAgIGNhc2UgJ3NpZ24tYWNjb3VudGluZy1leGNlcHQtemVybyc6XG4gICAgICAgIGNhc2UgJygpPyc6XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHNpZ25EaXNwbGF5OiAnZXhjZXB0WmVybycsXG4gICAgICAgICAgICAgICAgY3VycmVuY3lTaWduOiAnYWNjb3VudGluZycsXG4gICAgICAgICAgICB9O1xuICAgICAgICBjYXNlICdzaWduLW5ldmVyJzpcbiAgICAgICAgY2FzZSAnK18nOlxuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzaWduRGlzcGxheTogJ25ldmVyJyxcbiAgICAgICAgICAgIH07XG4gICAgfVxufVxuZnVuY3Rpb24gcGFyc2VDb25jaXNlU2NpZW50aWZpY0FuZEVuZ2luZWVyaW5nU3RlbShzdGVtKSB7XG4gICAgLy8gRW5naW5lZXJpbmdcbiAgICB2YXIgcmVzdWx0O1xuICAgIGlmIChzdGVtWzBdID09PSAnRScgJiYgc3RlbVsxXSA9PT0gJ0UnKSB7XG4gICAgICAgIHJlc3VsdCA9IHtcbiAgICAgICAgICAgIG5vdGF0aW9uOiAnZW5naW5lZXJpbmcnLFxuICAgICAgICB9O1xuICAgICAgICBzdGVtID0gc3RlbS5zbGljZSgyKTtcbiAgICB9XG4gICAgZWxzZSBpZiAoc3RlbVswXSA9PT0gJ0UnKSB7XG4gICAgICAgIHJlc3VsdCA9IHtcbiAgICAgICAgICAgIG5vdGF0aW9uOiAnc2NpZW50aWZpYycsXG4gICAgICAgIH07XG4gICAgICAgIHN0ZW0gPSBzdGVtLnNsaWNlKDEpO1xuICAgIH1cbiAgICBpZiAocmVzdWx0KSB7XG4gICAgICAgIHZhciBzaWduRGlzcGxheSA9IHN0ZW0uc2xpY2UoMCwgMik7XG4gICAgICAgIGlmIChzaWduRGlzcGxheSA9PT0gJyshJykge1xuICAgICAgICAgICAgcmVzdWx0LnNpZ25EaXNwbGF5ID0gJ2Fsd2F5cyc7XG4gICAgICAgICAgICBzdGVtID0gc3RlbS5zbGljZSgyKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChzaWduRGlzcGxheSA9PT0gJys/Jykge1xuICAgICAgICAgICAgcmVzdWx0LnNpZ25EaXNwbGF5ID0gJ2V4Y2VwdFplcm8nO1xuICAgICAgICAgICAgc3RlbSA9IHN0ZW0uc2xpY2UoMik7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFDT05DSVNFX0lOVEVHRVJfV0lEVEhfUkVHRVgudGVzdChzdGVtKSkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdNYWxmb3JtZWQgY29uY2lzZSBlbmcvc2NpZW50aWZpYyBub3RhdGlvbicpO1xuICAgICAgICB9XG4gICAgICAgIHJlc3VsdC5taW5pbXVtSW50ZWdlckRpZ2l0cyA9IHN0ZW0ubGVuZ3RoO1xuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xufVxuZnVuY3Rpb24gcGFyc2VOb3RhdGlvbk9wdGlvbnMob3B0KSB7XG4gICAgdmFyIHJlc3VsdCA9IHt9O1xuICAgIHZhciBzaWduT3B0cyA9IHBhcnNlU2lnbihvcHQpO1xuICAgIGlmIChzaWduT3B0cykge1xuICAgICAgICByZXR1cm4gc2lnbk9wdHM7XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG59XG4vKipcbiAqIGh0dHBzOi8vZ2l0aHViLmNvbS91bmljb2RlLW9yZy9pY3UvYmxvYi9tYXN0ZXIvZG9jcy91c2VyZ3VpZGUvZm9ybWF0X3BhcnNlL251bWJlcnMvc2tlbGV0b25zLm1kI3NrZWxldG9uLXN0ZW1zLWFuZC1vcHRpb25zXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwYXJzZU51bWJlclNrZWxldG9uKHRva2Vucykge1xuICAgIHZhciByZXN1bHQgPSB7fTtcbiAgICBmb3IgKHZhciBfaSA9IDAsIHRva2Vuc18xID0gdG9rZW5zOyBfaSA8IHRva2Vuc18xLmxlbmd0aDsgX2krKykge1xuICAgICAgICB2YXIgdG9rZW4gPSB0b2tlbnNfMVtfaV07XG4gICAgICAgIHN3aXRjaCAodG9rZW4uc3RlbSkge1xuICAgICAgICAgICAgY2FzZSAncGVyY2VudCc6XG4gICAgICAgICAgICBjYXNlICclJzpcbiAgICAgICAgICAgICAgICByZXN1bHQuc3R5bGUgPSAncGVyY2VudCc7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBjYXNlICcleDEwMCc6XG4gICAgICAgICAgICAgICAgcmVzdWx0LnN0eWxlID0gJ3BlcmNlbnQnO1xuICAgICAgICAgICAgICAgIHJlc3VsdC5zY2FsZSA9IDEwMDtcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIGNhc2UgJ2N1cnJlbmN5JzpcbiAgICAgICAgICAgICAgICByZXN1bHQuc3R5bGUgPSAnY3VycmVuY3knO1xuICAgICAgICAgICAgICAgIHJlc3VsdC5jdXJyZW5jeSA9IHRva2VuLm9wdGlvbnNbMF07XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBjYXNlICdncm91cC1vZmYnOlxuICAgICAgICAgICAgY2FzZSAnLF8nOlxuICAgICAgICAgICAgICAgIHJlc3VsdC51c2VHcm91cGluZyA9IGZhbHNlO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgY2FzZSAncHJlY2lzaW9uLWludGVnZXInOlxuICAgICAgICAgICAgY2FzZSAnLic6XG4gICAgICAgICAgICAgICAgcmVzdWx0Lm1heGltdW1GcmFjdGlvbkRpZ2l0cyA9IDA7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBjYXNlICdtZWFzdXJlLXVuaXQnOlxuICAgICAgICAgICAgY2FzZSAndW5pdCc6XG4gICAgICAgICAgICAgICAgcmVzdWx0LnN0eWxlID0gJ3VuaXQnO1xuICAgICAgICAgICAgICAgIHJlc3VsdC51bml0ID0gaWN1VW5pdFRvRWNtYSh0b2tlbi5vcHRpb25zWzBdKTtcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIGNhc2UgJ2NvbXBhY3Qtc2hvcnQnOlxuICAgICAgICAgICAgY2FzZSAnSyc6XG4gICAgICAgICAgICAgICAgcmVzdWx0Lm5vdGF0aW9uID0gJ2NvbXBhY3QnO1xuICAgICAgICAgICAgICAgIHJlc3VsdC5jb21wYWN0RGlzcGxheSA9ICdzaG9ydCc7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBjYXNlICdjb21wYWN0LWxvbmcnOlxuICAgICAgICAgICAgY2FzZSAnS0snOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5ub3RhdGlvbiA9ICdjb21wYWN0JztcbiAgICAgICAgICAgICAgICByZXN1bHQuY29tcGFjdERpc3BsYXkgPSAnbG9uZyc7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBjYXNlICdzY2llbnRpZmljJzpcbiAgICAgICAgICAgICAgICByZXN1bHQgPSBfX2Fzc2lnbihfX2Fzc2lnbihfX2Fzc2lnbih7fSwgcmVzdWx0KSwgeyBub3RhdGlvbjogJ3NjaWVudGlmaWMnIH0pLCB0b2tlbi5vcHRpb25zLnJlZHVjZShmdW5jdGlvbiAoYWxsLCBvcHQpIHsgcmV0dXJuIChfX2Fzc2lnbihfX2Fzc2lnbih7fSwgYWxsKSwgcGFyc2VOb3RhdGlvbk9wdGlvbnMob3B0KSkpOyB9LCB7fSkpO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgY2FzZSAnZW5naW5lZXJpbmcnOlxuICAgICAgICAgICAgICAgIHJlc3VsdCA9IF9fYXNzaWduKF9fYXNzaWduKF9fYXNzaWduKHt9LCByZXN1bHQpLCB7IG5vdGF0aW9uOiAnZW5naW5lZXJpbmcnIH0pLCB0b2tlbi5vcHRpb25zLnJlZHVjZShmdW5jdGlvbiAoYWxsLCBvcHQpIHsgcmV0dXJuIChfX2Fzc2lnbihfX2Fzc2lnbih7fSwgYWxsKSwgcGFyc2VOb3RhdGlvbk9wdGlvbnMob3B0KSkpOyB9LCB7fSkpO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgY2FzZSAnbm90YXRpb24tc2ltcGxlJzpcbiAgICAgICAgICAgICAgICByZXN1bHQubm90YXRpb24gPSAnc3RhbmRhcmQnO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3VuaWNvZGUtb3JnL2ljdS9ibG9iL21hc3Rlci9pY3U0Yy9zb3VyY2UvaTE4bi91bmljb2RlL3VudW1iZXJmb3JtYXR0ZXIuaFxuICAgICAgICAgICAgY2FzZSAndW5pdC13aWR0aC1uYXJyb3cnOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5jdXJyZW5jeURpc3BsYXkgPSAnbmFycm93U3ltYm9sJztcbiAgICAgICAgICAgICAgICByZXN1bHQudW5pdERpc3BsYXkgPSAnbmFycm93JztcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIGNhc2UgJ3VuaXQtd2lkdGgtc2hvcnQnOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5jdXJyZW5jeURpc3BsYXkgPSAnY29kZSc7XG4gICAgICAgICAgICAgICAgcmVzdWx0LnVuaXREaXNwbGF5ID0gJ3Nob3J0JztcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIGNhc2UgJ3VuaXQtd2lkdGgtZnVsbC1uYW1lJzpcbiAgICAgICAgICAgICAgICByZXN1bHQuY3VycmVuY3lEaXNwbGF5ID0gJ25hbWUnO1xuICAgICAgICAgICAgICAgIHJlc3VsdC51bml0RGlzcGxheSA9ICdsb25nJztcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIGNhc2UgJ3VuaXQtd2lkdGgtaXNvLWNvZGUnOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5jdXJyZW5jeURpc3BsYXkgPSAnc3ltYm9sJztcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIGNhc2UgJ3NjYWxlJzpcbiAgICAgICAgICAgICAgICByZXN1bHQuc2NhbGUgPSBwYXJzZUZsb2F0KHRva2VuLm9wdGlvbnNbMF0pO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgY2FzZSAncm91bmRpbmctbW9kZS1mbG9vcic6XG4gICAgICAgICAgICAgICAgcmVzdWx0LnJvdW5kaW5nTW9kZSA9ICdmbG9vcic7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBjYXNlICdyb3VuZGluZy1tb2RlLWNlaWxpbmcnOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5yb3VuZGluZ01vZGUgPSAnY2VpbCc7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBjYXNlICdyb3VuZGluZy1tb2RlLWRvd24nOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5yb3VuZGluZ01vZGUgPSAndHJ1bmMnO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgY2FzZSAncm91bmRpbmctbW9kZS11cCc6XG4gICAgICAgICAgICAgICAgcmVzdWx0LnJvdW5kaW5nTW9kZSA9ICdleHBhbmQnO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgY2FzZSAncm91bmRpbmctbW9kZS1oYWxmLWV2ZW4nOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5yb3VuZGluZ01vZGUgPSAnaGFsZkV2ZW4nO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgY2FzZSAncm91bmRpbmctbW9kZS1oYWxmLWRvd24nOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5yb3VuZGluZ01vZGUgPSAnaGFsZlRydW5jJztcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIGNhc2UgJ3JvdW5kaW5nLW1vZGUtaGFsZi11cCc6XG4gICAgICAgICAgICAgICAgcmVzdWx0LnJvdW5kaW5nTW9kZSA9ICdoYWxmRXhwYW5kJztcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIC8vIGh0dHBzOi8vdW5pY29kZS1vcmcuZ2l0aHViLmlvL2ljdS91c2VyZ3VpZGUvZm9ybWF0X3BhcnNlL251bWJlcnMvc2tlbGV0b25zLmh0bWwjaW50ZWdlci13aWR0aFxuICAgICAgICAgICAgY2FzZSAnaW50ZWdlci13aWR0aCc6XG4gICAgICAgICAgICAgICAgaWYgKHRva2VuLm9wdGlvbnMubGVuZ3RoID4gMSkge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgUmFuZ2VFcnJvcignaW50ZWdlci13aWR0aCBzdGVtcyBvbmx5IGFjY2VwdCBhIHNpbmdsZSBvcHRpb25hbCBvcHRpb24nKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdG9rZW4ub3B0aW9uc1swXS5yZXBsYWNlKElOVEVHRVJfV0lEVEhfUkVHRVgsIGZ1bmN0aW9uIChfLCBnMSwgZzIsIGczLCBnNCwgZzUpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGcxKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXN1bHQubWluaW11bUludGVnZXJEaWdpdHMgPSBnMi5sZW5ndGg7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSBpZiAoZzMgJiYgZzQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignV2UgY3VycmVudGx5IGRvIG5vdCBzdXBwb3J0IG1heGltdW0gaW50ZWdlciBkaWdpdHMnKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIGlmIChnNSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdXZSBjdXJyZW50bHkgZG8gbm90IHN1cHBvcnQgZXhhY3QgaW50ZWdlciBkaWdpdHMnKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gJyc7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgLy8gaHR0cHM6Ly91bmljb2RlLW9yZy5naXRodWIuaW8vaWN1L3VzZXJndWlkZS9mb3JtYXRfcGFyc2UvbnVtYmVycy9za2VsZXRvbnMuaHRtbCNpbnRlZ2VyLXdpZHRoXG4gICAgICAgIGlmIChDT05DSVNFX0lOVEVHRVJfV0lEVEhfUkVHRVgudGVzdCh0b2tlbi5zdGVtKSkge1xuICAgICAgICAgICAgcmVzdWx0Lm1pbmltdW1JbnRlZ2VyRGlnaXRzID0gdG9rZW4uc3RlbS5sZW5ndGg7XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoRlJBQ1RJT05fUFJFQ0lTSU9OX1JFR0VYLnRlc3QodG9rZW4uc3RlbSkpIHtcbiAgICAgICAgICAgIC8vIFByZWNpc2lvblxuICAgICAgICAgICAgLy8gaHR0cHM6Ly91bmljb2RlLW9yZy5naXRodWIuaW8vaWN1L3VzZXJndWlkZS9mb3JtYXRfcGFyc2UvbnVtYmVycy9za2VsZXRvbnMuaHRtbCNmcmFjdGlvbi1wcmVjaXNpb25cbiAgICAgICAgICAgIC8vIHByZWNpc2lvbi1pbnRlZ2VyIGNhc2VcbiAgICAgICAgICAgIGlmICh0b2tlbi5vcHRpb25zLmxlbmd0aCA+IDEpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgUmFuZ2VFcnJvcignRnJhY3Rpb24tcHJlY2lzaW9uIHN0ZW1zIG9ubHkgYWNjZXB0IGEgc2luZ2xlIG9wdGlvbmFsIG9wdGlvbicpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdG9rZW4uc3RlbS5yZXBsYWNlKEZSQUNUSU9OX1BSRUNJU0lPTl9SRUdFWCwgZnVuY3Rpb24gKF8sIGcxLCBnMiwgZzMsIGc0LCBnNSkge1xuICAgICAgICAgICAgICAgIC8vIC4wMDAqIGNhc2UgKGJlZm9yZSBJQ1U2NyBpdCB3YXMgLjAwMCspXG4gICAgICAgICAgICAgICAgaWYgKGcyID09PSAnKicpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0Lm1pbmltdW1GcmFjdGlvbkRpZ2l0cyA9IGcxLmxlbmd0aDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gLiMjIyBjYXNlXG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoZzMgJiYgZzNbMF0gPT09ICcjJykge1xuICAgICAgICAgICAgICAgICAgICByZXN1bHQubWF4aW11bUZyYWN0aW9uRGlnaXRzID0gZzMubGVuZ3RoO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyAuMDAjIyBjYXNlXG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoZzQgJiYgZzUpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0Lm1pbmltdW1GcmFjdGlvbkRpZ2l0cyA9IGc0Lmxlbmd0aDtcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0Lm1heGltdW1GcmFjdGlvbkRpZ2l0cyA9IGc0Lmxlbmd0aCArIGc1Lmxlbmd0aDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHJlc3VsdC5taW5pbXVtRnJhY3Rpb25EaWdpdHMgPSBnMS5sZW5ndGg7XG4gICAgICAgICAgICAgICAgICAgIHJlc3VsdC5tYXhpbXVtRnJhY3Rpb25EaWdpdHMgPSBnMS5sZW5ndGg7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiAnJztcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgdmFyIG9wdCA9IHRva2VuLm9wdGlvbnNbMF07XG4gICAgICAgICAgICAvLyBodHRwczovL3VuaWNvZGUtb3JnLmdpdGh1Yi5pby9pY3UvdXNlcmd1aWRlL2Zvcm1hdF9wYXJzZS9udW1iZXJzL3NrZWxldG9ucy5odG1sI3RyYWlsaW5nLXplcm8tZGlzcGxheVxuICAgICAgICAgICAgaWYgKG9wdCA9PT0gJ3cnKSB7XG4gICAgICAgICAgICAgICAgcmVzdWx0ID0gX19hc3NpZ24oX19hc3NpZ24oe30sIHJlc3VsdCksIHsgdHJhaWxpbmdaZXJvRGlzcGxheTogJ3N0cmlwSWZJbnRlZ2VyJyB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKG9wdCkge1xuICAgICAgICAgICAgICAgIHJlc3VsdCA9IF9fYXNzaWduKF9fYXNzaWduKHt9LCByZXN1bHQpLCBwYXJzZVNpZ25pZmljYW50UHJlY2lzaW9uKG9wdCkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgLy8gaHR0cHM6Ly91bmljb2RlLW9yZy5naXRodWIuaW8vaWN1L3VzZXJndWlkZS9mb3JtYXRfcGFyc2UvbnVtYmVycy9za2VsZXRvbnMuaHRtbCNzaWduaWZpY2FudC1kaWdpdHMtcHJlY2lzaW9uXG4gICAgICAgIGlmIChTSUdOSUZJQ0FOVF9QUkVDSVNJT05fUkVHRVgudGVzdCh0b2tlbi5zdGVtKSkge1xuICAgICAgICAgICAgcmVzdWx0ID0gX19hc3NpZ24oX19hc3NpZ24oe30sIHJlc3VsdCksIHBhcnNlU2lnbmlmaWNhbnRQcmVjaXNpb24odG9rZW4uc3RlbSkpO1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgdmFyIHNpZ25PcHRzID0gcGFyc2VTaWduKHRva2VuLnN0ZW0pO1xuICAgICAgICBpZiAoc2lnbk9wdHMpIHtcbiAgICAgICAgICAgIHJlc3VsdCA9IF9fYXNzaWduKF9fYXNzaWduKHt9LCByZXN1bHQpLCBzaWduT3B0cyk7XG4gICAgICAgIH1cbiAgICAgICAgdmFyIGNvbmNpc2VTY2llbnRpZmljQW5kRW5naW5lZXJpbmdPcHRzID0gcGFyc2VDb25jaXNlU2NpZW50aWZpY0FuZEVuZ2luZWVyaW5nU3RlbSh0b2tlbi5zdGVtKTtcbiAgICAgICAgaWYgKGNvbmNpc2VTY2llbnRpZmljQW5kRW5naW5lZXJpbmdPcHRzKSB7XG4gICAgICAgICAgICByZXN1bHQgPSBfX2Fzc2lnbihfX2Fzc2lnbih7fSwgcmVzdWx0KSwgY29uY2lzZVNjaWVudGlmaWNBbmRFbmdpbmVlcmluZ09wdHMpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/number.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WHITE_SPACE_REGEX: () => (/* binding */ WHITE_SPACE_REGEX)\n/* harmony export */ });\n// @generated from regex-gen.ts\nvar WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LXNrZWxldG9uLXBhcnNlci9saWIvcmVnZXguZ2VuZXJhdGVkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFwcCBEZXZcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcaW50ZXJuYWwtdmVcXG5vZGVfbW9kdWxlc1xcQGZvcm1hdGpzXFxpY3Utc2tlbGV0b24tcGFyc2VyXFxsaWJcXHJlZ2V4LmdlbmVyYXRlZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBAZ2VuZXJhdGVkIGZyb20gcmVnZXgtZ2VuLnRzXG5leHBvcnQgdmFyIFdISVRFX1NQQUNFX1JFR0VYID0gL1tcXHQtXFxyIFxceDg1XFx1MjAwRVxcdTIwMEZcXHUyMDI4XFx1MjAyOV0vaTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/intl-messageformat/lib/src/core.js":
/*!*********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/core.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlMessageFormat: () => (/* binding */ IntlMessageFormat)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/fast-memoize */ \"(app-pages-browser)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _formatters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./formatters */ \"(app-pages-browser)/./node_modules/intl-messageformat/lib/src/formatters.js\");\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\n\n\n\n\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, (c1 || {})), (c2 || {})), Object.keys(c1).reduce(function (all, k) {\n        all[k] = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, c1[k]), (c2[k] || {}));\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function (all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function () {\n            return {\n                get: function (key) {\n                    return store[key];\n                },\n                set: function (key, value) {\n                    store[key] = value;\n                },\n            };\n        },\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) { cache = {\n        number: {},\n        dateTime: {},\n        pluralRules: {},\n    }; }\n    return {\n        getNumberFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n        getDateTimeFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n        getPluralRules: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n    };\n}\nvar IntlMessageFormat = /** @class */ (function () {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        if (locales === void 0) { locales = IntlMessageFormat.defaultLocale; }\n        var _this = this;\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {},\n        };\n        this.format = function (values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function (all, part) {\n                if (!all.length ||\n                    part.type !== _formatters__WEBPACK_IMPORTED_MODULE_3__.PART_TYPE.literal ||\n                    typeof all[all.length - 1] !== 'string') {\n                    all.push(part.value);\n                }\n                else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || '';\n            }\n            return result;\n        };\n        this.formatToParts = function (values) {\n            return (0,_formatters__WEBPACK_IMPORTED_MODULE_3__.formatToParts)(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function () {\n            var _a;\n            return ({\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) ||\n                    Intl.NumberFormat.supportedLocalesOf(_this.locales)[0],\n            });\n        };\n        this.getAst = function () { return _this.ast; };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === 'string') {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError('IntlMessageFormat.__parse must be set to process `message` of type `string`');\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__rest)(_a, [\"formatters\"]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, parseOpts), { locale: this.resolvedLocale }));\n        }\n        else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError('A message must be provided as a String or AST.');\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters =\n            (opts && opts.formatters) || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function () {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale =\n                    new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function (locales) {\n        if (typeof Intl.Locale === 'undefined') {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === 'string' ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__.parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0,\n            },\n            currency: {\n                style: 'currency',\n            },\n            percent: {\n                style: 'percent',\n            },\n        },\n        date: {\n            short: {\n                month: 'numeric',\n                day: 'numeric',\n                year: '2-digit',\n            },\n            medium: {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            long: {\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            full: {\n                weekday: 'long',\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n        },\n        time: {\n            short: {\n                hour: 'numeric',\n                minute: 'numeric',\n            },\n            medium: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n            },\n            long: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n            full: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n        },\n    };\n    return IntlMessageFormat;\n}());\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/intl-messageformat/lib/src/core.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/intl-messageformat/lib/src/error.js":
/*!**********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/error.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   FormatError: () => (/* binding */ FormatError),\n/* harmony export */   InvalidValueError: () => (/* binding */ InvalidValueError),\n/* harmony export */   InvalidValueTypeError: () => (/* binding */ InvalidValueTypeError),\n/* harmony export */   MissingValueError: () => (/* binding */ MissingValueError)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n\nvar ErrorCode;\n(function (ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (ErrorCode = {}));\nvar FormatError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function () {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error));\n\nvar InvalidValueError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, \"Invalid values for \\\"\".concat(variableId, \"\\\": \\\"\").concat(value, \"\\\". Options are \\\"\").concat(Object.keys(options).join('\", \"'), \"\\\"\"), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError));\n\nvar InvalidValueTypeError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, \"Value for \\\"\".concat(value, \"\\\" must be of type \").concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError));\n\nvar MissingValueError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, \"The intl string context variable \\\"\".concat(variableId, \"\\\" was not provided to the string \\\"\").concat(originalMessage, \"\\\"\"), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError));\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/intl-messageformat/lib/src/error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/intl-messageformat/lib/src/formatters.js":
/*!***************************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/formatters.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PART_TYPE: () => (/* binding */ PART_TYPE),\n/* harmony export */   formatToParts: () => (/* binding */ formatToParts),\n/* harmony export */   isFormatXMLElementFn: () => (/* binding */ isFormatXMLElementFn)\n/* harmony export */ });\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error */ \"(app-pages-browser)/./node_modules/intl-messageformat/lib/src/error.js\");\n\n\nvar PART_TYPE;\n(function (PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function (all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart ||\n            lastPart.type !== PART_TYPE.literal ||\n            part.type !== PART_TYPE.literal) {\n            all.push(part);\n        }\n        else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nfunction isFormatXMLElementFn(el) {\n    return typeof el === 'function';\n}\n// TODO(skeleton): add skeleton support\nfunction formatToParts(els, locales, formatters, formats, values, currentPluralValue, \n// For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value,\n            },\n        ];\n    }\n    var result = [];\n    for (var _i = 0, els_1 = els; _i < els_1.length; _i++) {\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value,\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPoundElement)(el)) {\n            if (typeof currentPluralValue === 'number') {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue),\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new _error__WEBPACK_IMPORTED_MODULE_1__.MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isArgumentElement)(el)) {\n            if (!value || typeof value === 'string' || typeof value === 'number') {\n                value =\n                    typeof value === 'string' || typeof value === 'number'\n                        ? String(value)\n                        : '';\n            }\n            result.push({\n                type: typeof value === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                value: value,\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.date[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTimeElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.time[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.number[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            if (style && style.scale) {\n                value =\n                    value *\n                        (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getNumberFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueTypeError(value_1, 'function', originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function (p) { return p.value; }));\n            if (!Array.isArray(chunks)) {\n                chunks = [chunks];\n            }\n            result.push.apply(result, chunks.map(function (c) {\n                return {\n                    type: typeof c === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c,\n                };\n            }));\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new _error__WEBPACK_IMPORTED_MODULE_1__.FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", _error__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters\n                    .getPluralRules(locales, { type: el.pluralType })\n                    .select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/intl-messageformat/lib/src/formatters.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NextIntlClientProvider)\n/* harmony export */ });\n/* harmony import */ var use_intl_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/react */ \"(app-pages-browser)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction NextIntlClientProvider(param) {\n    let { locale, ...rest } = param;\n    if (!locale) {\n        throw new Error(\"Couldn't infer the `locale` prop in `NextIntlClientProvider`, please provide it explicitly.\\n\\nSee https://next-intl.dev/docs/configuration#locale\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(use_intl_react__WEBPACK_IMPORTED_MODULE_1__.IntlProvider, {\n        locale: locale,\n        ...rest\n    });\n}\n_c = NextIntlClientProvider;\n\nvar _c;\n$RefreshReg$(_c, \"NextIntlClientProvider\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vZGV2ZWxvcG1lbnQvc2hhcmVkL05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OzZEQUM4QztBQUNOO0FBRXhDLFNBQVNFLHVCQUF1QixLQUcvQjtRQUgrQixFQUM5QkMsTUFBTSxFQUNOLEdBQUdDLE1BQ0osR0FIK0I7SUFJOUIsSUFBSSxDQUFDRCxRQUFRO1FBQ1gsTUFBTSxJQUFJRSxNQUFNO0lBQ2xCO0lBQ0EsT0FBTyxXQUFXLEdBQUVKLHNEQUFHQSxDQUFDRCx3REFBWUEsRUFBRTtRQUNwQ0csUUFBUUE7UUFDUixHQUFHQyxJQUFJO0lBQ1Q7QUFDRjtLQVhTRjtBQWFvQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBcHAgRGV2XFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGludGVybmFsLXZlXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxkZXZlbG9wbWVudFxcc2hhcmVkXFxOZXh0SW50bENsaWVudFByb3ZpZGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IHsgSW50bFByb3ZpZGVyIH0gZnJvbSAndXNlLWludGwvcmVhY3QnO1xuaW1wb3J0IHsganN4IH0gZnJvbSAncmVhY3QvanN4LXJ1bnRpbWUnO1xuXG5mdW5jdGlvbiBOZXh0SW50bENsaWVudFByb3ZpZGVyKHtcbiAgbG9jYWxlLFxuICAuLi5yZXN0XG59KSB7XG4gIGlmICghbG9jYWxlKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiQ291bGRuJ3QgaW5mZXIgdGhlIGBsb2NhbGVgIHByb3AgaW4gYE5leHRJbnRsQ2xpZW50UHJvdmlkZXJgLCBwbGVhc2UgcHJvdmlkZSBpdCBleHBsaWNpdGx5LlxcblxcblNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9jb25maWd1cmF0aW9uI2xvY2FsZVwiICk7XG4gIH1cbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9qc3goSW50bFByb3ZpZGVyLCB7XG4gICAgbG9jYWxlOiBsb2NhbGUsXG4gICAgLi4ucmVzdFxuICB9KTtcbn1cblxuZXhwb3J0IHsgTmV4dEludGxDbGllbnRQcm92aWRlciBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOlsiSW50bFByb3ZpZGVyIiwianN4IiwiTmV4dEludGxDbGllbnRQcm92aWRlciIsImxvY2FsZSIsInJlc3QiLCJFcnJvciIsImRlZmF1bHQiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CApp%20Dev%5C%5CDocuments%5C%5Caugment-projects%5C%5Cinternal-ve%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CApp%20Dev%5C%5CDocuments%5C%5Caugment-projects%5C%5Cinternal-ve%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQXBwJTIwRGV2JTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q2ludGVybmFsLXZlJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dC1pbnRsJTVDJTVDZGlzdCU1QyU1Q2VzbSU1QyU1Q2RldmVsb3BtZW50JTVDJTVDc2hhcmVkJTVDJTVDTmV4dEludGxDbGllbnRQcm92aWRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxzUkFBa04iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxBcHAgRGV2XFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGludGVybmFsLXZlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0LWludGxcXFxcZGlzdFxcXFxlc21cXFxcZGV2ZWxvcG1lbnRcXFxcc2hhcmVkXFxcXE5leHRJbnRsQ2xpZW50UHJvdmlkZXIuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CApp%20Dev%5C%5CDocuments%5C%5Caugment-projects%5C%5Cinternal-ve%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs":
/*!******************************************!*\
  !*** ./node_modules/tslib/tslib.es6.mjs ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __addDisposableResource: () => (/* binding */ __addDisposableResource),\n/* harmony export */   __assign: () => (/* binding */ __assign),\n/* harmony export */   __asyncDelegator: () => (/* binding */ __asyncDelegator),\n/* harmony export */   __asyncGenerator: () => (/* binding */ __asyncGenerator),\n/* harmony export */   __asyncValues: () => (/* binding */ __asyncValues),\n/* harmony export */   __await: () => (/* binding */ __await),\n/* harmony export */   __awaiter: () => (/* binding */ __awaiter),\n/* harmony export */   __classPrivateFieldGet: () => (/* binding */ __classPrivateFieldGet),\n/* harmony export */   __classPrivateFieldIn: () => (/* binding */ __classPrivateFieldIn),\n/* harmony export */   __classPrivateFieldSet: () => (/* binding */ __classPrivateFieldSet),\n/* harmony export */   __createBinding: () => (/* binding */ __createBinding),\n/* harmony export */   __decorate: () => (/* binding */ __decorate),\n/* harmony export */   __disposeResources: () => (/* binding */ __disposeResources),\n/* harmony export */   __esDecorate: () => (/* binding */ __esDecorate),\n/* harmony export */   __exportStar: () => (/* binding */ __exportStar),\n/* harmony export */   __extends: () => (/* binding */ __extends),\n/* harmony export */   __generator: () => (/* binding */ __generator),\n/* harmony export */   __importDefault: () => (/* binding */ __importDefault),\n/* harmony export */   __importStar: () => (/* binding */ __importStar),\n/* harmony export */   __makeTemplateObject: () => (/* binding */ __makeTemplateObject),\n/* harmony export */   __metadata: () => (/* binding */ __metadata),\n/* harmony export */   __param: () => (/* binding */ __param),\n/* harmony export */   __propKey: () => (/* binding */ __propKey),\n/* harmony export */   __read: () => (/* binding */ __read),\n/* harmony export */   __rest: () => (/* binding */ __rest),\n/* harmony export */   __rewriteRelativeImportExtension: () => (/* binding */ __rewriteRelativeImportExtension),\n/* harmony export */   __runInitializers: () => (/* binding */ __runInitializers),\n/* harmony export */   __setFunctionName: () => (/* binding */ __setFunctionName),\n/* harmony export */   __spread: () => (/* binding */ __spread),\n/* harmony export */   __spreadArray: () => (/* binding */ __spreadArray),\n/* harmony export */   __spreadArrays: () => (/* binding */ __spreadArrays),\n/* harmony export */   __values: () => (/* binding */ __values),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nfunction __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nvar __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nfunction __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nfunction __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nfunction __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nfunction __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nfunction __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nfunction __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nfunction __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nfunction __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nfunction __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nfunction __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nvar __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nfunction __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nfunction __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nfunction __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nfunction __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nfunction __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nfunction __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nfunction __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nfunction __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nfunction __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nfunction __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nfunction __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nfunction __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nfunction __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nfunction __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nfunction __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nfunction __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nfunction __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nfunction __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I: () => (/* binding */ IntlError),\n/* harmony export */   a: () => (/* binding */ IntlErrorCode),\n/* harmony export */   b: () => (/* binding */ createIntlFormatters),\n/* harmony export */   c: () => (/* binding */ createFormatter),\n/* harmony export */   d: () => (/* binding */ createCache),\n/* harmony export */   e: () => (/* binding */ createBaseTranslator),\n/* harmony export */   f: () => (/* binding */ defaultGetMessageFallback),\n/* harmony export */   g: () => (/* binding */ defaultOnError),\n/* harmony export */   i: () => (/* binding */ initializeConfig),\n/* harmony export */   r: () => (/* binding */ resolveNamespace)\n/* harmony export */ });\n/* harmony import */ var intl_messageformat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! intl-messageformat */ \"(app-pages-browser)/./node_modules/intl-messageformat/lib/src/core.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @formatjs/fast-memoize */ \"(app-pages-browser)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\n\n\n\nclass IntlError extends Error {\n  constructor(code, originalMessage) {\n    let message = code;\n    if (originalMessage) {\n      message += ': ' + originalMessage;\n    }\n    super(message);\n    this.code = code;\n    if (originalMessage) {\n      this.originalMessage = originalMessage;\n    }\n  }\n}\n\nvar IntlErrorCode = /*#__PURE__*/function (IntlErrorCode) {\n  IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n  IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n  IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n  IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n  IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n  IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n  IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n  return IntlErrorCode;\n}(IntlErrorCode || {});\n\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */\nfunction convertFormatsToIntlMessageFormat(globalFormats, inlineFormats, timeZone) {\n  const mfDateDefaults = intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat.formats.date;\n  const mfTimeDefaults = intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat.formats.time;\n  const dateTimeFormats = {\n    ...globalFormats?.dateTime,\n    ...inlineFormats?.dateTime\n  };\n  const allFormats = {\n    date: {\n      ...mfDateDefaults,\n      ...dateTimeFormats\n    },\n    time: {\n      ...mfTimeDefaults,\n      ...dateTimeFormats\n    },\n    number: {\n      ...globalFormats?.number,\n      ...inlineFormats?.number\n    }\n    // (list is not supported in ICU messages)\n  };\n  if (timeZone) {\n    // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n    // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n    ['date', 'time'].forEach(property => {\n      const formats = allFormats[property];\n      for (const [key, value] of Object.entries(formats)) {\n        formats[key] = {\n          timeZone,\n          ...value\n        };\n      }\n    });\n  }\n  return allFormats;\n}\n\nfunction joinPath(...parts) {\n  return parts.filter(Boolean).join('.');\n}\n\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */\n\nfunction defaultGetMessageFallback(props) {\n  return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n  console.error(error);\n}\n\nfunction createCache() {\n  return {\n    dateTime: {},\n    number: {},\n    message: {},\n    relativeTime: {},\n    pluralRules: {},\n    list: {},\n    displayNames: {}\n  };\n}\nfunction createMemoCache(store) {\n  return {\n    create() {\n      return {\n        get(key) {\n          return store[key];\n        },\n        set(key, value) {\n          store[key] = value;\n        }\n      };\n    }\n  };\n}\nfunction memoFn(fn, cache) {\n  return (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__.memoize)(fn, {\n    cache: createMemoCache(cache),\n    strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__.strategies.variadic\n  });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n  return memoFn((...args) => new ConstructorFn(...args), cache);\n}\nfunction createIntlFormatters(cache) {\n  const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n  const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n  const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n  const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n  const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n  const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n  return {\n    getDateTimeFormat,\n    getNumberFormat,\n    getPluralRules,\n    getRelativeTimeFormat,\n    getListFormat,\n    getDisplayNames\n  };\n}\n\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n  const getMessageFormat = memoFn((...args) => new intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat(args[0], args[1], args[2], {\n    formatters: intlFormatters,\n    ...args[3]\n  }), cache.message);\n  return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n  const fullKey = joinPath(namespace, key);\n  if (!messages) {\n    throw new Error(`No messages available at \\`${namespace}\\`.` );\n  }\n  let message = messages;\n  key.split('.').forEach(part => {\n    const next = message[part];\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (part == null || next == null) {\n      throw new Error(`Could not resolve \\`${fullKey}\\` in messages for locale \\`${locale}\\`.` );\n    }\n    message = next;\n  });\n  return message;\n}\nfunction prepareTranslationValues(values) {\n  // Workaround for https://github.com/formatjs/formatjs/issues/1467\n  const transformedValues = {};\n  Object.keys(values).forEach(key => {\n    let index = 0;\n    const value = values[key];\n    let transformed;\n    if (typeof value === 'function') {\n      transformed = chunks => {\n        const result = value(chunks);\n        return /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(result) ? /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(result, {\n          key: key + index++\n        }) : result;\n      };\n    } else {\n      transformed = value;\n    }\n    transformedValues[key] = transformed;\n  });\n  return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace, onError = defaultOnError) {\n  try {\n    if (!messages) {\n      throw new Error(`No messages were configured.` );\n    }\n    const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (!retrievedMessages) {\n      throw new Error(`No messages for namespace \\`${namespace}\\` found.` );\n    }\n    return retrievedMessages;\n  } catch (error) {\n    const intlError = new IntlError(IntlErrorCode.MISSING_MESSAGE, error.message);\n    onError(intlError);\n    return intlError;\n  }\n}\nfunction getPlainMessage(candidate, values) {\n  {\n    // Keep fast path in development\n    if (values) return undefined;\n\n    // Despite potentially no values being available, there can still be\n    // placeholders in the message if the user has forgotten to provide\n    // values. In this case we compile the message to receive an error.\n    const unescapedMessage = candidate.replace(/'([{}])/gi, '$1');\n    const hasPlaceholders = /<|{/.test(unescapedMessage);\n    if (!hasPlaceholders) {\n      return unescapedMessage;\n    }\n  }\n}\nfunction createBaseTranslator(config) {\n  const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n  return createBaseTranslatorImpl({\n    ...config,\n    messagesOrError\n  });\n}\nfunction createBaseTranslatorImpl({\n  cache,\n  formats: globalFormats,\n  formatters,\n  getMessageFallback = defaultGetMessageFallback,\n  locale,\n  messagesOrError,\n  namespace,\n  onError,\n  timeZone\n}) {\n  const hasMessagesError = messagesOrError instanceof IntlError;\n  function getFallbackFromErrorAndNotify(key, code, message) {\n    const error = new IntlError(code, message);\n    onError(error);\n    return getMessageFallback({\n      error,\n      key,\n      namespace\n    });\n  }\n  function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    let message;\n    try {\n      message = resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n    if (typeof message === 'object') {\n      let code, errorMessage;\n      if (Array.isArray(message)) {\n        code = IntlErrorCode.INVALID_MESSAGE;\n        {\n          errorMessage = `Message at \\`${joinPath(namespace, key)}\\` resolved to an array, but only strings are supported. See https://next-intl.dev/docs/usage/messages#arrays-of-messages`;\n        }\n      } else {\n        code = IntlErrorCode.INSUFFICIENT_PATH;\n        {\n          errorMessage = `Message at \\`${joinPath(namespace, key)}\\` resolved to an object, but only strings are supported. Use a \\`.\\` to retrieve nested messages. See https://next-intl.dev/docs/usage/messages#structuring-messages`;\n        }\n      }\n      return getFallbackFromErrorAndNotify(key, code, errorMessage);\n    }\n    let messageFormat;\n\n    // Hot path that avoids creating an `IntlMessageFormat` instance\n    const plainMessage = getPlainMessage(message, values);\n    if (plainMessage) return plainMessage;\n\n    // Lazy init the message formatter for better tree\n    // shaking in case message formatting is not used.\n    if (!formatters.getMessageFormat) {\n      formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n    }\n    try {\n      messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat(globalFormats, formats, timeZone), {\n        formatters: {\n          ...formatters,\n          getDateTimeFormat(locales, options) {\n            // Workaround for https://github.com/formatjs/formatjs/issues/4279\n            return formatters.getDateTimeFormat(locales, {\n              timeZone,\n              ...options\n            });\n          }\n        }\n      });\n    } catch (error) {\n      const thrownError = error;\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.INVALID_MESSAGE, thrownError.message + ('originalMessage' in thrownError ? ` (${thrownError.originalMessage})` : '') );\n    }\n    try {\n      const formattedMessage = messageFormat.format(\n      // @ts-expect-error `intl-messageformat` expects a different format\n      // for rich text elements since a recent minor update. This\n      // needs to be evaluated in detail, possibly also in regards\n      // to be able to format to parts.\n      values ? prepareTranslationValues(values) : values);\n      if (formattedMessage == null) {\n        throw new Error(`Unable to format \\`${key}\\` in ${namespace ? `namespace \\`${namespace}\\`` : 'messages'}` );\n      }\n\n      // Limit the function signature to return strings or React elements\n      return /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(formattedMessage) ||\n      // Arrays of React elements\n      Array.isArray(formattedMessage) || typeof formattedMessage === 'string' ? formattedMessage : String(formattedMessage);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.FORMATTING_ERROR, error.message);\n    }\n  }\n  function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    const result = translateBaseFn(key, values, formats);\n    if (typeof result !== 'string') {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.INVALID_MESSAGE, `The message \\`${key}\\` in ${namespace ? `namespace \\`${namespace}\\`` : 'messages'} didn't resolve to a string. If you want to format rich text, use \\`t.rich\\` instead.` );\n    }\n    return result;\n  }\n  translateFn.rich = translateBaseFn;\n\n  // Augment `translateBaseFn` to return plain strings\n  translateFn.markup = (key, values, formats) => {\n    const result = translateBaseFn(key,\n    // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n    // of `RichTranslationValues` but TypeScript isn't smart enough here.\n    values, formats);\n    if (typeof result !== 'string') {\n      const error = new IntlError(IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\");\n      onError(error);\n      return getMessageFallback({\n        error,\n        key,\n        namespace\n      });\n    }\n    return result;\n  };\n  translateFn.raw = key => {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    try {\n      return resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n  };\n  translateFn.has = key => {\n    if (hasMessagesError) {\n      return false;\n    }\n    try {\n      resolvePath(locale, messagesOrError, key, namespace);\n      return true;\n    } catch {\n      return false;\n    }\n  };\n  return translateFn;\n}\n\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */\nfunction resolveNamespace(namespace, namespacePrefix) {\n  return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + '.').length);\n}\n\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n  second: SECOND,\n  seconds: SECOND,\n  minute: MINUTE,\n  minutes: MINUTE,\n  hour: HOUR,\n  hours: HOUR,\n  day: DAY,\n  days: DAY,\n  week: WEEK,\n  weeks: WEEK,\n  month: MONTH,\n  months: MONTH,\n  quarter: QUARTER,\n  quarters: QUARTER,\n  year: YEAR,\n  years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n  const absValue = Math.abs(seconds);\n  if (absValue < MINUTE) {\n    return 'second';\n  } else if (absValue < HOUR) {\n    return 'minute';\n  } else if (absValue < DAY) {\n    return 'hour';\n  } else if (absValue < WEEK) {\n    return 'day';\n  } else if (absValue < MONTH) {\n    return 'week';\n  } else if (absValue < YEAR) {\n    return 'month';\n  }\n  return 'year';\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n  // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n  // will include fractions like '2.1 hours ago'.\n  return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(props) {\n  const {\n    _cache: cache = createCache(),\n    _formatters: formatters = createIntlFormatters(cache),\n    formats,\n    locale,\n    onError = defaultOnError,\n    timeZone: globalTimeZone\n  } = props;\n  function applyTimeZone(options) {\n    if (!options?.timeZone) {\n      if (globalTimeZone) {\n        options = {\n          ...options,\n          timeZone: globalTimeZone\n        };\n      } else {\n        onError(new IntlError(IntlErrorCode.ENVIRONMENT_FALLBACK, `The \\`timeZone\\` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#time-zone` ));\n      }\n    }\n    return options;\n  }\n  function resolveFormatOrOptions(typeFormats, formatOrOptions, overrides) {\n    let options;\n    if (typeof formatOrOptions === 'string') {\n      const formatName = formatOrOptions;\n      options = typeFormats?.[formatName];\n      if (!options) {\n        const error = new IntlError(IntlErrorCode.MISSING_FORMAT, `Format \\`${formatName}\\` is not available.` );\n        onError(error);\n        throw error;\n      }\n    } else {\n      options = formatOrOptions;\n    }\n    if (overrides) {\n      options = {\n        ...options,\n        ...overrides\n      };\n    }\n    return options;\n  }\n  function getFormattedValue(formatOrOptions, overrides, typeFormats, formatter, getFallback) {\n    let options;\n    try {\n      options = resolveFormatOrOptions(typeFormats, formatOrOptions, overrides);\n    } catch {\n      return getFallback();\n    }\n    try {\n      return formatter(options);\n    } catch (error) {\n      onError(new IntlError(IntlErrorCode.FORMATTING_ERROR, error.message));\n      return getFallback();\n    }\n  }\n  function dateTime(value, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).format(value);\n    }, () => String(value));\n  }\n  function dateTimeRange(start, end, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n    }, () => [dateTime(start), dateTime(end)].join(' – '));\n  }\n  function number(value, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.number, options => formatters.getNumberFormat(locale, options).format(value), () => String(value));\n  }\n  function getGlobalNow() {\n    // Only read when necessary to avoid triggering a `dynamicIO` error\n    // unnecessarily (`now` is only needed for `format.relativeTime`)\n    if (props.now) {\n      return props.now;\n    } else {\n      onError(new IntlError(IntlErrorCode.ENVIRONMENT_FALLBACK, `The \\`now\\` parameter wasn't provided to \\`relativeTime\\` and there is no global default configured, therefore the current time will be used as a fallback. See https://next-intl.dev/docs/usage/dates-times#relative-times-usenow` ));\n      return new Date();\n    }\n  }\n  function relativeTime(date, nowOrOptions) {\n    try {\n      let nowDate, unit;\n      const opts = {};\n      if (nowOrOptions instanceof Date || typeof nowOrOptions === 'number') {\n        nowDate = new Date(nowOrOptions);\n      } else if (nowOrOptions) {\n        if (nowOrOptions.now != null) {\n          nowDate = new Date(nowOrOptions.now);\n        } else {\n          nowDate = getGlobalNow();\n        }\n        unit = nowOrOptions.unit;\n        opts.style = nowOrOptions.style;\n        // @ts-expect-error -- Types are slightly outdated\n        opts.numberingSystem = nowOrOptions.numberingSystem;\n      }\n      if (!nowDate) {\n        nowDate = getGlobalNow();\n      }\n      const dateDate = new Date(date);\n      const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n      if (!unit) {\n        unit = resolveRelativeTimeUnit(seconds);\n      }\n\n      // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n      // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n      // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n      // not desired, as the given dates might cross a threshold were the\n      // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n      // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n      // case. By using `always` we can ensure correct output. The only exception\n      // is the formatting of times <1 second as \"now\".\n      opts.numeric = unit === 'second' ? 'auto' : 'always';\n      const value = calculateRelativeTimeValue(seconds, unit);\n      return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n    } catch (error) {\n      onError(new IntlError(IntlErrorCode.FORMATTING_ERROR, error.message));\n      return String(date);\n    }\n  }\n  function list(value, formatOrOptions, overrides) {\n    const serializedValue = [];\n    const richValues = new Map();\n\n    // `formatToParts` only accepts strings, therefore we have to temporarily\n    // replace React elements with a placeholder ID that can be used to retrieve\n    // the original value afterwards.\n    let index = 0;\n    for (const item of value) {\n      let serializedItem;\n      if (typeof item === 'object') {\n        serializedItem = String(index);\n        richValues.set(serializedItem, item);\n      } else {\n        serializedItem = String(item);\n      }\n      serializedValue.push(serializedItem);\n      index++;\n    }\n    return getFormattedValue(formatOrOptions, overrides, formats?.list,\n    // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n    options => {\n      const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map(part => part.type === 'literal' ? part.value : richValues.get(part.value) || part.value);\n      if (richValues.size > 0) {\n        return result;\n      } else {\n        return result.join('');\n      }\n    }, () => String(value));\n  }\n  return {\n    dateTime,\n    number,\n    relativeTime,\n    list,\n    dateTimeRange\n  };\n}\n\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n  Object.entries(messages).forEach(([key, messageOrMessages]) => {\n    if (key.includes('.')) {\n      let keyLabel = key;\n      if (parentPath) keyLabel += ` (at ${parentPath})`;\n      invalidKeyLabels.push(keyLabel);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (messageOrMessages != null && typeof messageOrMessages === 'object') {\n      validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n    }\n  });\n}\nfunction validateMessages(messages, onError) {\n  const invalidKeyLabels = [];\n  validateMessagesSegment(messages, invalidKeyLabels);\n  if (invalidKeyLabels.length > 0) {\n    onError(new IntlError(IntlErrorCode.INVALID_KEY, `Namespace keys can not contain the character \".\" as this is used to express nesting. Please remove it or replace it with another character.\n\nInvalid ${invalidKeyLabels.length === 1 ? 'key' : 'keys'}: ${invalidKeyLabels.join(', ')}\n\nIf you're migrating from a flat structure, you can convert your messages as follows:\n\nimport {set} from \"lodash\";\n\nconst input = {\n  \"one.one\": \"1.1\",\n  \"one.two\": \"1.2\",\n  \"two.one.one\": \"2.1.1\"\n};\n\nconst output = Object.entries(input).reduce(\n  (acc, [key, value]) => set(acc, key, value),\n  {}\n);\n\n// Output:\n//\n// {\n//   \"one\": {\n//     \"one\": \"1.1\",\n//     \"two\": \"1.2\"\n//   },\n//   \"two\": {\n//     \"one\": {\n//       \"one\": \"2.1.1\"\n//     }\n//   }\n// }\n` ));\n  }\n}\n\n/**\n * Enhances the incoming props with defaults.\n */\nfunction initializeConfig({\n  formats,\n  getMessageFallback,\n  messages,\n  onError,\n  ...rest\n}) {\n  const finalOnError = onError || defaultOnError;\n  const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n  {\n    if (messages) {\n      validateMessages(messages, finalOnError);\n    }\n  }\n  return {\n    ...rest,\n    formats: formats || undefined,\n    messages: messages || undefined,\n    onError: finalOnError,\n    getMessageFallback: finalGetMessageFallback\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/use-intl/dist/esm/development/react.js":
/*!*************************************************************!*\
  !*** ./node_modules/use-intl/dist/esm/development/react.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlProvider: () => (/* binding */ IntlProvider),\n/* harmony export */   useFormatter: () => (/* binding */ useFormatter),\n/* harmony export */   useLocale: () => (/* binding */ useLocale),\n/* harmony export */   useMessages: () => (/* binding */ useMessages),\n/* harmony export */   useNow: () => (/* binding */ useNow),\n/* harmony export */   useTimeZone: () => (/* binding */ useTimeZone),\n/* harmony export */   useTranslations: () => (/* binding */ useTranslations)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./initializeConfig-CRD6euuK.js */ \"(app-pages-browser)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n\n\n\n\n\n\nconst IntlContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\n\nfunction IntlProvider({\n  children,\n  formats,\n  getMessageFallback,\n  locale,\n  messages,\n  now,\n  onError,\n  timeZone\n}) {\n  const prevContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(IntlContext);\n\n  // The formatter cache is released when the locale changes. For\n  // long-running apps with a persistent `IntlProvider` at the root,\n  // this can reduce the memory footprint (e.g. in React Native).\n  const cache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    return prevContext?.cache || (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.d)();\n  }, [locale, prevContext?.cache]);\n  const formatters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => prevContext?.formatters || (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.b)(cache), [cache, prevContext?.formatters]);\n\n  // Memoizing this value helps to avoid triggering a re-render of all\n  // context consumers in case the configuration didn't change. However,\n  // if some of the non-primitive values change, a re-render will still\n  // be triggered. Note that there's no need to put `memo` on `IntlProvider`\n  // itself, because the `children` typically change on every render.\n  // There's some burden on the consumer side if it's important to reduce\n  // re-renders, put that's how React works.\n  // See: https://blog.isquaredsoftware.com/2020/05/blogged-answers-a-mostly-complete-guide-to-react-rendering-behavior/#context-updates-and-render-optimizations\n  const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    ...(0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.i)({\n      locale,\n      // (required by provider)\n      formats: formats === undefined ? prevContext?.formats : formats,\n      getMessageFallback: getMessageFallback || prevContext?.getMessageFallback,\n      messages: messages === undefined ? prevContext?.messages : messages,\n      now: now || prevContext?.now,\n      onError: onError || prevContext?.onError,\n      timeZone: timeZone || prevContext?.timeZone\n    }),\n    formatters,\n    cache\n  }), [cache, formats, formatters, getMessageFallback, locale, messages, now, onError, prevContext, timeZone]);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(IntlContext.Provider, {\n    value: value,\n    children: children\n  });\n}\n\nfunction useIntlContext() {\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(IntlContext);\n  if (!context) {\n    throw new Error('No intl context found. Have you configured the provider? See https://next-intl.dev/docs/usage/configuration#server-client-components' );\n  }\n  return context;\n}\n\nlet hasWarnedForMissingTimezone = false;\nconst isServer = typeof window === 'undefined';\nfunction useTranslationsImpl(allMessagesPrefixed, namespacePrefixed, namespacePrefix) {\n  const {\n    cache,\n    formats: globalFormats,\n    formatters,\n    getMessageFallback,\n    locale,\n    onError,\n    timeZone\n  } = useIntlContext();\n\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the hook invocation.\n  const allMessages = allMessagesPrefixed[namespacePrefix];\n  const namespace = (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.r)(namespacePrefixed, namespacePrefix);\n  if (!timeZone && !hasWarnedForMissingTimezone && isServer) {\n    // eslint-disable-next-line react-compiler/react-compiler\n    hasWarnedForMissingTimezone = true;\n    onError(new _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.I(_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.a.ENVIRONMENT_FALLBACK, `There is no \\`timeZone\\` configured, this can lead to markup mismatches caused by environment differences. Consider adding a global default: https://next-intl.dev/docs/configuration#time-zone` ));\n  }\n  const translate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.e)({\n    cache,\n    formatters,\n    getMessageFallback,\n    messages: allMessages,\n    namespace,\n    onError,\n    formats: globalFormats,\n    locale,\n    timeZone\n  }), [cache, formatters, getMessageFallback, allMessages, namespace, onError, globalFormats, locale, timeZone]);\n  return translate;\n}\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction useTranslations(namespace) {\n  const context = useIntlContext();\n  const messages = context.messages;\n\n  // We have to wrap the actual hook so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  // @ts-expect-error Use the explicit annotation instead\n  return useTranslationsImpl({\n    '!': messages\n  },\n  // @ts-expect-error\n  namespace ? `!.${namespace}` : '!', '!');\n}\n\nfunction useLocale() {\n  return useIntlContext().locale;\n}\n\nfunction getNow() {\n  return new Date();\n}\n\n/**\n * @see https://next-intl.dev/docs/usage/dates-times#relative-times-usenow\n */\nfunction useNow(options) {\n  const updateInterval = options?.updateInterval;\n  const {\n    now: globalNow\n  } = useIntlContext();\n  const [now, setNow] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(globalNow || getNow());\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!updateInterval) return;\n    const intervalId = setInterval(() => {\n      setNow(getNow());\n    }, updateInterval);\n    return () => {\n      clearInterval(intervalId);\n    };\n  }, [globalNow, updateInterval]);\n  return updateInterval == null && globalNow ? globalNow : now;\n}\n\nfunction useTimeZone() {\n  return useIntlContext().timeZone;\n}\n\nfunction useMessages() {\n  const context = useIntlContext();\n  if (!context.messages) {\n    throw new Error('No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages' );\n  }\n  return context.messages;\n}\n\nfunction useFormatter() {\n  const {\n    formats,\n    formatters,\n    locale,\n    now: globalNow,\n    onError,\n    timeZone\n  } = useIntlContext();\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.c)({\n    formats,\n    locale,\n    now: globalNow,\n    onError,\n    timeZone,\n    _formatters: formatters\n  }), [formats, formatters, globalNow, locale, onError, timeZone]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/use-intl/dist/esm/development/react.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CApp%20Dev%5C%5CDocuments%5C%5Caugment-projects%5C%5Cinternal-ve%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);