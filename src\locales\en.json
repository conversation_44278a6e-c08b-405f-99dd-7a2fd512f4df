{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "actions": "Actions", "yes": "Yes", "no": "No", "confirm": "Confirm", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information"}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "fullName": "Full Name", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "signInWithEmail": "Sign in with email", "signUpWithEmail": "Sign up with email", "checkEmail": "Check your email for the confirmation link", "passwordResetSent": "Password reset email sent", "invalidCredentials": "Invalid email or password", "emailRequired": "Email is required", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 6 characters", "passwordsDoNotMatch": "Passwords do not match"}, "navigation": {"dashboard": "Dashboard", "organizations": "Organizations", "facilities": "Facilities", "users": "Users", "settings": "Settings", "profile": "Profile"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome back!", "overview": "Overview", "recentActivity": "Recent Activity", "quickActions": "Quick Actions"}, "organizations": {"title": "Organizations", "createOrganization": "Create Organization", "organizationName": "Organization Name", "description": "Description", "noOrganizations": "No organizations found", "organizationCreated": "Organization created successfully", "organizationUpdated": "Organization updated successfully", "organizationDeleted": "Organization deleted successfully"}, "facilities": {"title": "Facilities", "createFacility": "Create Facility", "facilityName": "Facility Name", "address": "Address", "noFacilities": "No facilities found", "facilityCreated": "Facility created successfully", "facilityUpdated": "Facility updated successfully", "facilityDeleted": "Facility deleted successfully"}, "users": {"title": "Users", "inviteUser": "Invite User", "userEmail": "User Email", "role": "Role", "noUsers": "No users found", "userInvited": "User invited successfully", "userUpdated": "User updated successfully", "userRemoved": "User removed successfully"}, "roles": {"admin": "Admin", "manager": "Manager", "staff": "Staff"}}