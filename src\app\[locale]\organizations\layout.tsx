'use client'

import { use } from 'react'
import { UserProvider } from '@/components/auth/user-provider'
import { DashboardLayout } from '@/components/layout/dashboard-layout'

export default function OrganizationsLayoutWrapper({
  children,
  params
}: {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}) {
  const { locale } = use(params)
  return (
    <UserProvider>
      <DashboardLayout locale={locale}>
        {children}
      </DashboardLayout>
    </UserProvider>
  )
}
