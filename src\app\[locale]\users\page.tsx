'use client'

import { useTranslations } from 'next-intl'
import { useUser } from '@/components/auth/user-provider'

export default function UsersPage({ params }: { params: { locale: string } }) {
  const locale = params.locale
  const t = useTranslations()
  const { user } = useUser()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {t('users.title')}
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            Manage users and their roles within your organizations.
          </p>
        </div>
      </div>

      {/* Coming Soon */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="text-center py-8">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              User Management
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              User management features are coming soon. You'll be able to invite users, assign roles, and manage permissions.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
