import { supabase } from './supabase'
import type { User } from './supabase'

export interface AuthUser extends User {
  userRoles?: Array<{
    organization_id: string
    facility_id?: string
    role: {
      name: string
      permissions: Array<{
        name: string
        resource: string
        action: string
      }>
    }
  }>
}

export const authService = {
  // Sign up with email and password
  async signUp(email: string, password: string, fullName?: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
      },
    })

    if (error) throw error

    // Create user profile
    if (data.user) {
      const { error: profileError } = await supabase
        .from('users')
        .insert({
          id: data.user.id,
          email: data.user.email!,
          full_name: fullName,
        })

      if (profileError) throw profileError
    }

    return data
  },

  // Sign in with email and password
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) throw error
    return data
  },

  // Sign out
  async signOut() {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  },

  // Reset password
  async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    })

    if (error) throw error
  },

  // Update password
  async updatePassword(password: string) {
    const { error } = await supabase.auth.updateUser({
      password,
    })

    if (error) throw error
  },

  // Get current user with roles and permissions
  async getCurrentUser(): Promise<AuthUser | null> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) return null

    const { data: userProfile, error } = await supabase
      .from('users')
      .select(`
        *,
        user_roles:user_roles(
          organization_id,
          facility_id,
          role:roles(
            name,
            role_permissions:role_permissions(
              permission:permissions(
                name,
                resource,
                action
              )
            )
          )
        )
      `)
      .eq('id', user.id)
      .single()

    if (error) throw error

    // Transform the data to match our interface
    const authUser: AuthUser = {
      ...userProfile,
      userRoles: userProfile.user_roles?.map((ur: any) => ({
        organization_id: ur.organization_id,
        facility_id: ur.facility_id,
        role: {
          name: ur.role.name,
          permissions: ur.role.role_permissions?.map((rp: any) => rp.permission) || [],
        },
      })) || [],
    }

    return authUser
  },

  // Check if user has permission
  hasPermission(user: AuthUser | null, permission: string, organizationId?: string): boolean {
    if (!user || !user.userRoles) return false

    return user.userRoles.some(userRole => {
      // If organizationId is specified, check only roles in that organization
      if (organizationId && userRole.organization_id !== organizationId) {
        return false
      }

      return userRole.role.permissions.some(p => p.name === permission)
    })
  },

  // Get user's organizations
  getUserOrganizations(user: AuthUser | null): string[] {
    if (!user || !user.userRoles) return []

    return [...new Set(user.userRoles.map(ur => ur.organization_id))]
  },
}
